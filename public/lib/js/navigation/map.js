var vectors,
    controlSelect,
    LAYER_ZP_PLOTS = 1,
    LAYER_TEMP_DATA = 2,
    LAYER_KOMAS_PLOTS = 4,
    LAYER_KVS_PLOTS = 5,
    LAYER_ISAK_PLOTS = 6,
    LAYER_FOR_ISAK_PLOTS = 9,
    LAYER_TYPE_LFA = 10,
    LAYER_TYPE_NATURA_2000 = 11,
    LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12,
    LAYER_TYPE_VPS_PASISHTA = 13,
    LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14,
    LAYER_TYPE_VPS_GASKI_ZIMNI = 15,
    LAYER_TYPE_VPS_LIVADEN_BLATAR = 16,
    LAYER_TYPE_VPS_ORLI_LESHOYADI = 17;

var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

var mapControls = {};

function initControls() {
    mapControls = {
        line: new OpenLayers.Control.DynamicMeasure(
            OpenLayers.Handler.Path,
            {
                persist: false,
                geodesic: true
            }
        ),
        polygon: new OpenLayers.Control.DynamicMeasure(
            OpenLayers.Handler.Polygon,
            {
                persist: false,
                geodesic: true
            }
        ),
        zoomin: new OpenLayers.Control.ZoomBox({
            title: "Zoom in box",
            out: false
        }),
        zoomout: new OpenLayers.Control.ZoomBox({
            title: "Zoom out box",
            out: true
        }),
        drawABLine: ab_lines.createABLineControll()
    };

    var control;
    for (var key in mapControls) {
        control = mapControls[key];
        map.addControl(control);
    }
}



OpenLayers.ProxyHost = "/cgi-bin/proxy.cgi?url=";

function chooseControl(name) {
    for (key in mapControls) {
        var control = mapControls[key];
        if (name == key) {
            control.activate();
        } else {
            control.deactivate();
        }
    }

    map.events.unregister('click', map, getZPlotData);
}

function unselectAll() {
    jQuery('#tool-zoomin').linkbutton('unselect');
    jQuery('#tool-zoomout').linkbutton('unselect');
    jQuery('#tool-measure-line').linkbutton('unselect');
    jQuery('#tool-measure-polygon').linkbutton('unselect');
    jQuery('#tool-panzoom').linkbutton('unselect');
    jQuery('#tool-select-zplot').linkbutton('unselect');
    jQuery("#tool-abline-add").linkbutton("unselect");
    jQuery("#tool-abline-save").linkbutton({disabled: true});
    jQuery("#tool-abline-copy").linkbutton({disabled: true});
    jQuery("#tool-abline-projection").linkbutton("unselect");
}

function initMap() {
    var options = {
        controls: [
            new OpenLayers.Control.Navigation(),
            new OpenLayers.Control.ScaleLine({ bottomInUnits: "km" })
        ],
        projection: new OpenLayers.Projection("EPSG:900913"),
        theme: null,
        numZoomLevels: 19
    };
    map = new OpenLayers.Map('map', options);
    initMapPad();
    map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
        new OpenLayers.Projection("EPSG:32635"),
        map.getProjectionObject())
    );
}

function loadMapMessages(data) {
    //remove old wialon messages
    removeLayerByName('Следа от машина');

    if (!data.length) {
        jQuery.messager.alert('Грешка', "Няма намерени данни!");
        return;
    }

    var proj_4326 = new OpenLayers.Projection('EPSG:4326');
    var proj_900913 = new OpenLayers.Projection('EPSG:900913');

    var style = {
        strokeColor: '#00ff00',
        strokeOpacity: 1,
        strokeWidth: 3
    };

    var olPointsFeatureArr = jQuery.map(data, function (point) {
        if (!point.lat || !point.lon) {
            return null; //skipping the point.
        }
        var olPoint = new OpenLayers.Geometry.Point(point.lon, point.lat).transform(proj_4326, proj_900913);
        return new OpenLayers.Feature.Vector(olPoint, null, style);
    });

    var machineTrackLayer = new OpenLayers.Layer.PointTrack('Следа от машина', {
        // strategies: [new OpenLayers.Strategy.Fixed()],
        styleFrom: OpenLayers.Layer.PointTrack.SOURCE_NODE
    });

    machineTrackLayer.addNodes(olPointsFeatureArr);
    map.addLayer(machineTrackLayer);
    map.zoomToExtent(machineTrackLayer.getDataExtent());
}

function removeLayerByName(layer_name) {
    var layers = map.getLayersByName(layer_name);
    for (var i = 0; i < layers.length; i++) {
        map.removeLayer(layers[i]);
    }
}

function initVectorLayers() {
    var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
    renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

    vectors = new OpenLayers.Layer.Vector("Очертания", {
        //displayInLayerSwitcher: false,
        styleMap: new OpenLayers.StyleMap(new OpenLayers.Style({
                strokeColor: "${strokeColor}",
                strokeWidth: 3,
                fillOpacity: 0.2,
                label: '${label}',
                fontColor: "${fontColor}",
                fontSize: "${fontSize}",
                fontWeight: "${fontWeight}",
            }, {
                context: {
                    strokeColor: function (feature) {
                        if (feature.attributes.hasOwnProperty('type') && feature.attributes.type === "abLine") {
                            return  "#ff0000";
                        }

                        return "#4fffff";
                    },
                    label: function (feature) {
                        if(feature.attributes.hasOwnProperty('type') && feature.attributes.type !== "abLine"){
                             return feature.attributes.type === "start abLine" ?  "A" : "B ";
                        }

                        if (!feature.attributes.hasOwnProperty('label') || map.getZoom() < 13) {
                            return '';
                        }
                        return feature.attributes.label;
                    },
                    fontColor: function (feature) {
                        if (feature.attributes.hasOwnProperty('type')) {
                            return  "#fff";
                        }

                        return "#4fffff";
                    },
                    fontSize: function (feature) {  
                        if (map.getZoom() < 13) {
                            return 0;
                        }

                        if (feature.attributes.hasOwnProperty('type')) {
                            return  '24px';
                        }

                        return '12px';
                    },
                    fontWeight: function (feature) {
                        if (feature.attributes.hasOwnProperty('type')) {
                            return  "bold";
                        }

                        return "normal";
                    }
                    
                }
            }
        )),
        renderers: renderer
    });

    map.addLayer(vectors);
    vectors.removeAllFeatures();
}

function loadMapLayers(node) {
    if (node['attributes'].layer_name && node['_checked']) {
        if (node['attributes'].layer_type == 'layer_allowable'
            || node['attributes'].layer_type == LAYER_TYPE_LFA
            || node['attributes'].layer_type == LAYER_TYPE_NATURA_2000
            || node['attributes'].layer_type == LAYER_TYPE_PERMANETELY_GREEN_AREAS
            || node['attributes'].layer_type == LAYER_TYPE_VPS_PASISHTA
            || node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
            || node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_ZIMNI
            || node['attributes'].layer_type == LAYER_TYPE_VPS_LIVADEN_BLATAR
            || node['attributes'].layer_type == LAYER_TYPE_VPS_ORLI_LESHOYADI) {
            if (node['attributes'].layer_name == 'layer_allowable') {
                loadMapLayerAllowable();
            } else if (node['attributes'].layer_name == 'layer_allowable_final') {
                loadMapLayerAllowableFinal();
            } else {
                loadMapRemoteLayer(node['attributes'].layer_name);
            }
        }
        else {
            loadMapLayer(node['attributes'].layer_name, node['attributes'].extent);
        }
    }
}

function loadMapLayer(layer_name, extent, options) {
    options = options || {};
    if (layer_name && extent) {
        var layerExtent = new OpenLayers.Bounds.fromString(extent).transform(
            new OpenLayers.Projection("EPSG:32635"),
            map.getProjectionObject()
        );
        var layerData = new OpenLayers.Layer.WMS(
            layer_name,
            wmsServer + "?map=" + mapPath + groupID + ".map",
            Object.assign({}, {
                layers: layer_name,
                format: "image/png",
                transparent: "true"
            }, options)
        );
        map.addLayer(layerData);
        layerData.redraw(true);

        pushLayerToTopPosition("Очертания");
        pushLayerToTopPosition("ABLines");
    }
}

function loadMapLayerAllowable() {
    var layers = map.getLayersByName('layer_allowable');

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        'layer_allowable',
        login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_draft.map',
        {
            layers: 'layer_allowable_draft',
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushLayerToTopPosition("Очертания");
}

function loadMapLayerAllowableFinal() {
    var layers = map.getLayersByName('layer_allowable_final');

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        'layer_allowable_final',
        login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_final.map',
        {
            layers: 'layer_allowable_final',
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushLayerToTopPosition("Очертания");
}

function loadMapRemoteLayer(layer_name) {
    var layers = map.getLayersByName(layer_name);

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        layer_name,
        login3WmsServer + "?map=" + login3MapPath + layer_name + '.map',
        {
            layers: layer_name,
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);
    //vector layer should always be the top layer
    pushLayerToTopPosition("Очертания");
}

function zoomToLayerExtent() {
    var layer_tree_data = jQuery('#all-layers-tree').tree('find', context_layer);

    map.zoomToExtent(new OpenLayers.Bounds.fromString(layer_tree_data['attributes'].extent).transform(
        new OpenLayers.Projection("EPSG:32635"),
        map.getProjectionObject())
    );
}

function removeFeaturesByIndex(index) {
    var features = vectors.features;
    const featuresForRemoval = [];

    for(var i=0; i<features.length; i++) {
        if(features[i].attributes.index == index) {
            featuresForRemoval.push(features[i]);
        }
    }
    
    vectors.removeFeatures(featuresForRemoval);
}

/**
 * 
 * @param {geoJSON} featureCollection 
 * @param {bool} removeFeatures 
 */
function previewLineFeatureCollection(featureCollection, removeFeatures = false)
{
    if(removeFeatures) {
        vectors.removeAllFeatures();
    }
    
    const geoJSON = JSON.parse(featureCollection.geoJSON);
    let vectorNavigationLayer = navigation_preview.getNavigationPreviewLayer();

    vectorNavigationLayer.setZIndex(parseInt(1000));
    vectorNavigationLayer.removeAllFeatures();
    
    geoJSON.features.forEach((element, index) => {
        displayNavigationLineFeature(element.geometry);
    });
}

/**
 * 
 * @param {string} geom 
 */
function displayNavigationLineFeature(geom)
{
    let vectorNavigationLayer = navigation_preview.getNavigationPreviewLayer();

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:4326")
    };
    
    let features = new OpenLayers.Format.GeoJSON(in_options).read(geom);

    if (features) {
        if (features.constructor != Array) {
            features = [features];
        }
        
        vectorNavigationLayer.addFeatures(features);
    }
}

//called on tree selection
function displayFeatureSelection(geom, label, format = 'WKT', removeFeatures = true, showLabel = true) {
    //remove all previous features
    if(removeFeatures) vectors.removeAllFeatures();
    
    ab_lines.getAbLinesVectorLayer().removeAllFeatures();
    jQuery("#tool-abline-projection").linkbutton("unselect");

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:32635")
    };
    
    var features;
    if(format === 'GEO_JSON') {
        features = new OpenLayers.Format.GeoJSON(in_options).read(geom);
    } else {
        features = new OpenLayers.Format.WKT(in_options).read(geom);
    }

    var bounds = null;

    if (features) {
        if (features.constructor != Array) {
            features = [features];
        }
        for (var i = 0; i < features.length; ++i) {
            if(showLabel) features[i].attributes.label = label;
            features[i].attributes.index = label;

            if (!bounds) {
                bounds = features[i].geometry.getBounds();
            } else {
                bounds.extend(features[i].geometry.getBounds());
            }

            if(geom.type === 'MultiLineString') {
                features[i].attributes.type = 'abLine';
                makeStartEndAbLineFeatures(vectors, features[i], label);
            }
        }

        vectors.addFeatures(features);
        var b = bounds.getCenterLonLat();
        var lonlat = new OpenLayers.LonLat(b.lon, b.lat);
        map.panTo(lonlat);
    }
    if (bounds) {
        return bounds;
    }
}

function makeStartEndAbLineFeatures(layer, lineFeature, index) {
    var startPoint = lineFeature.geometry.getVertices()[0];
    var endPoint = lineFeature.geometry.getVertices()[lineFeature.geometry.getVertices().length - 1];
    
    var startFeature = new OpenLayers.Feature.Vector(
        new OpenLayers.Geometry.Point(startPoint.x, startPoint.y),
        {
            type: "start abLine",
            index: index
    }
    );
    var endFeature = new OpenLayers.Feature.Vector(
        new OpenLayers.Geometry.Point(endPoint.x, endPoint.y),
        {
            type: "end abLine",
            index: index
        }
    );
    
    layer.addFeatures([startFeature, endFeature]);
}


function selectZPlotTreeNode(data) {
    if (data.error == false) {
        var node = ZP_TREE_CTRL.tree('find', data.zplot_id).target;
        ZP_TREE_CTRL.tree('check', node);
        ZP_TREE_CTRL.tree('select', node);
        ZP_TREE_CTRL.tree('scrollTo', node);
        displayFeatureSelection(data.geom, '');
    }
}

function pushLayerToTopPosition(layerName) {
    var layers = map.layers;
    var layersCount = layers.length;
    var foundLayers = map.getLayersByName(layerName);
    var topIdx = layersCount - 1;
    if (!foundLayers.length) {
        return;
    }
    var layer = foundLayers[0];
    map.setLayerIndex(layer, topIdx);
}

function initMapPad(specific_map_type) {
    var chosenMapType;
    var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

    //on init map type will not be specified
    if (specific_map_type == undefined) {
        chosenMapType = store.get('map_pad') || 6;
    }
    //when map type is changed specific_map_type will have the value of map type
    else {
        chosenMapType = parseInt(specific_map_type);
    }

    var layerMapPad;

    switch (chosenMapType) {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan'
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type == undefined) {
        map.addLayer(layerMapPad);
    }
    else {
        map.addLayer(layerMapPad);
        map.setLayerIndex(map.layers[map.layers.length - 1], 0);
        map.removeLayer(map.layers[1]);
        map.layers[0].redraw(true);
    }
}
