var vectors,
    controlSelect,
    LAYER_ZP_PLOTS = 1,
    LAYER_TEMP_DATA = 2,
    LAYER_KOMAS_PLOTS = 4,
    LAYER_KVS_PLOTS = 5,
    LAYER_ISAK_PLOTS = 6,
    LAYER_FOR_ISAK_PLOTS = 9,
    LAYER_TYPE_LFA = 10,
    LAYER_TYPE_NATURA_2000 = 11,
    LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12,
    LAYER_TYPE_VPS_PASISHTA = 13,
    LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14,
    LAYER_TYPE_VPS_GASKI_ZIMNI = 15,
    LAYER_TYPE_VPS_LIVADEN_BLATAR = 16,
    LAYER_TYPE_VPS_ORLI_LESHOYADI = 17;

var mapControls = {
    line: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
        persist: false,
        geodesic: true
    }),
    polygon: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
        persist: false,
        geodesic: true
    }),
    zoomin: new OpenLayers.Control.ZoomBox({
        title: "Zoom in box",
        out: false
    }),
    zoomout: new OpenLayers.Control.ZoomBox({
        title: "Zoom out box",
        out: true
    })
};

var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

OpenLayers.ProxyHost = "/cgi-bin/proxy.cgi?url=";

function chooseControl(name) {
    for (key in mapControls) {
        var control = mapControls[key];
        if (name == key) {
            control.activate();
        } else {
            control.deactivate();
        }
    }

    map.events.unregister('click', map, getZPlotData);
}

function unselectAll() {
    jQuery('#tool-zoomin').linkbutton('unselect');
    jQuery('#tool-zoomout').linkbutton('unselect');
    jQuery('#tool-measure-line').linkbutton('unselect');
    jQuery('#tool-measure-polygon').linkbutton('unselect');
    jQuery('#tool-panzoom').linkbutton('unselect');
    jQuery('#tool-select-zplot').linkbutton('unselect');
}

function initMap() {
    var options = {
        controls: [new OpenLayers.Control.Navigation(), new OpenLayers.Control.ScaleLine({bottomInUnits: 'km'})],
        projection: new OpenLayers.Projection("EPSG:900913"),
        theme: null
    };
    map = new OpenLayers.Map('map', options);
    initMapPad();
    map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
        new OpenLayers.Projection("EPSG:32635"),
        map.getProjectionObject())
    );

    var control;
    for (var key in mapControls) {
        control = mapControls[key];
        map.addControl(control);
    }
}

function loadMapMessages(data) {
    //remove old wialon messages
    removeLayerByName('Следа от машина');

    if (!data.length) {
        jQuery.messager.alert('Грешка', "Няма намерени данни!");
        return;
    }

    var proj_4326 = new OpenLayers.Projection('EPSG:4326');
    var proj_900913 = new OpenLayers.Projection('EPSG:900913');

    var style = {
        strokeColor: '#00ff00',
        strokeOpacity: 1,
        strokeWidth: 3
    };

    var olPointsFeatureArr = jQuery.map(data, function (point) {
        if (!point.lat || !point.lon) {
            return null; //skipping the point.
        }
        var olPoint = new OpenLayers.Geometry.Point(point.lon, point.lat).transform(proj_4326, proj_900913);
        return new OpenLayers.Feature.Vector(olPoint, null, style);
    });

    var machineTrackLayer = new OpenLayers.Layer.PointTrack('Следа от машина', {
        // strategies: [new OpenLayers.Strategy.Fixed()],
        styleFrom: OpenLayers.Layer.PointTrack.SOURCE_NODE
    });

    machineTrackLayer.addNodes(olPointsFeatureArr);
    map.addLayer(machineTrackLayer);
    map.zoomToExtent(machineTrackLayer.getDataExtent());
}

function removeLayerByName(layer_name) {
    var layers = map.getLayersByName(layer_name);
    for (var i = 0; i < layers.length; i++) {
        map.removeLayer(layers[i]);
    }
}

function initVectorLayers() {
    var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
    renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

    vectors = new OpenLayers.Layer.Vector("Очертания", {
        //displayInLayerSwitcher: false,
        styleMap: new OpenLayers.StyleMap(new OpenLayers.Style({
                strokeColor: "#4fffff",
                strokeWidth: 3,
                fillOpacity: 0.2,
                fillColor: "#4fffff",
                label: '${label}'
            }, {
                context: {
                    label: function (feature) {
                        if (!feature.attributes.hasOwnProperty('label') || map.getZoom() < 13) {
                            return '';
                        }
                        return feature.attributes.label;
                    }
                }
            }
        )),
        renderers: renderer
    });

    map.addLayers([vectors]);

    vectors.removeAllFeatures();
}

function loadMapLayers(node) {
    if (node['attributes'].layer_name && node['_checked']) {
        if (node['attributes'].layer_type == 'layer_allowable'
            || node['attributes'].layer_type == LAYER_TYPE_LFA
            || node['attributes'].layer_type == LAYER_TYPE_NATURA_2000
            || node['attributes'].layer_type == LAYER_TYPE_PERMANETELY_GREEN_AREAS
            || node['attributes'].layer_type == LAYER_TYPE_VPS_PASISHTA
            || node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
            || node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_ZIMNI
            || node['attributes'].layer_type == LAYER_TYPE_VPS_LIVADEN_BLATAR
            || node['attributes'].layer_type == LAYER_TYPE_VPS_ORLI_LESHOYADI) {
            if (node['attributes'].layer_name == 'layer_allowable') {
                loadMapLayerAllowable();
            } else if (node['attributes'].layer_name == 'layer_allowable_final') {
                loadMapLayerAllowableFinal();
            } else {
                loadMapRemoteLayer(node['attributes'].layer_name);
            }
        }
        else {
            loadMapLayer(node['attributes'].layer_name, node['attributes'].extent);
        }
    }
}

function loadMapLayer(layer_name, extent) {
    if (layer_name && extent) {
        var layerExtent = new OpenLayers.Bounds.fromString(extent).transform(
            new OpenLayers.Projection("EPSG:32635"),
            map.getProjectionObject()
        );
        const isSubUrl = wmsServer.indexOf('?') !== -1;
        const urlSeparator = isSubUrl ? '&' : '?';
        var layerData = new OpenLayers.Layer.WMS(
            layer_name,
            `${wmsServer}${urlSeparator}map=${mapPath}${groupID}.map`,
            {
                layers: layer_name,
                format: 'image/png',
                transparent: "true"
            });
        map.addLayer(layerData);
        layerData.redraw(true);

        pushVectorLayerToTopPosition();
    }
}

function loadMapLayerAllowable() {
    var layers = map.getLayersByName('layer_allowable');

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        'layer_allowable',
        login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_draft.map',
        {
            layers: 'layer_allowable_draft',
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function loadMapLayerAllowableFinal() {
    var layers = map.getLayersByName('layer_allowable_final');

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        'layer_allowable_final',
        login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_final.map',
        {
            layers: 'layer_allowable_final',
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function loadMapRemoteLayer(layer_name) {
    var layers = map.getLayersByName(layer_name);

    if (layers.length > 0) {
        return;
    }

    var layerData = new OpenLayers.Layer.WMS(
        layer_name,
        login3WmsServer + "?map=" + login3MapPath + layer_name + '.map',
        {
            layers: layer_name,
            format: 'image/png',
            transparent: "true"
        });
    map.addLayer(layerData);
    layerData.redraw(true);
    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function zoomToLayerExtent() {
    var layer_tree_data = jQuery('#all-layers-tree').tree('find', context_layer);

    map.zoomToExtent(new OpenLayers.Bounds.fromString(layer_tree_data['attributes'].extent).transform(
        new OpenLayers.Projection("EPSG:32635"),
        map.getProjectionObject())
    );
}
//called on tree selection
function displayFeatureSelection(geom, label) {
    //remove all previous features
    vectors.removeAllFeatures();

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:32635")
    };
    var features = new OpenLayers.Format.WKT(in_options).read(geom);

    var bounds = null;

    if (features) {
    if (features.constructor != Array) {
        features = [features];
    }
    for (var i = 0; i < features.length; ++i) {
        features[i].attributes.label = label;

        if (!bounds) {
            bounds = features[i].geometry.getBounds();
        } else {
            bounds.extend(features[i].geometry.getBounds());
        }
    }

    vectors.addFeatures(features);
    var b = bounds.getCenterLonLat();
    var lonlat = new OpenLayers.LonLat(b.lon, b.lat);
    map.panTo(lonlat);
    }
    if (bounds) {
        return bounds;
    }
}

function selectZPlotTreeNode(data) {
    if (data.error == false) {
        var node = ZP_TREE_CTRL.tree('find', data.zplot_id).target;
        ZP_TREE_CTRL.tree('select', node);
        ZP_TREE_CTRL.tree('scrollTo', node);
        displayFeatureSelection(data.geom, '');
    } else {
        vectors.removeAllFeatures();
    }
}

function pushVectorLayerToTopPosition() {
    var layers = map.layers;
    var layers_count = layers.length;

    if (map.layers[layers_count - 1].name != "Очертания") {
        for (var i = 0; i < map.layers.length; i++) {
            if (layers[i].name == "Очертания") {
                map.setLayerIndex(map.layers[i], layers.length - 1);
            }
        }
    }
}

function initMapPad(specific_map_type) {
    var chosenMapType;
    var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
    var tmpFeatures = [];
    //on init map type will not be specified
    if (specific_map_type == undefined) {
        chosenMapType = store.get('map_pad') || 6;
    }
    //when map type is changed specific_map_type will have the value of map type
    else {
        chosenMapType = parseInt(specific_map_type);
    }

    var layerMapPad;

    switch (chosenMapType) {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                "MapPad",
                {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 20}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan'
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type == undefined) {
        map.addLayer(layerMapPad);
    }
    else {
        if (vectors.features.length > 0) {
            tmpFeatures = vectors.features;
            vectors.removeAllFeatures();
        }
        map.addLayer(layerMapPad);
        map.setLayerIndex(map.layers[map.layers.length - 1], 0);
        map.removeLayer(map.layers[1]);
        map.layers[0].redraw(true);
        if (tmpFeatures.length > 0) {
            vectors.addFeatures(tmpFeatures);
        }
    }
}