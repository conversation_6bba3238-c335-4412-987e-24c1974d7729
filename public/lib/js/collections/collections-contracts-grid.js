function initCollectionsContracts() {
    var date = new Date();

    jQuery('#collection-contracts-table').datagrid({
        url: 'index.php?collections-rpc=collections-contracts-grid',
        nowrap: true,
        rownumbers: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        idField: 'id',
        sortName: 'c_num',
        border: true,
        sortOrder: 'asc',
        rpcParams: [getCollectionContractsFilters()],
        rowStyler: function(index, row) {
            var style = [];
            if (row.payday !== undefined) {
                var payday_arr = row.payday.split(" ");
                if (payday_arr[1] !== undefined) {
                    payday_month_obj = jQuery.grep(months, function(e) {
                        return e.label == payday_arr[1];
                    });

                    if (parseFloat(row.unpaid) > 0) {
                        if (payday_month_obj[0].value < date.getMonth()) {
                            style.push('background-color: #f7b7b7');
                        } else if (payday_month_obj[0].value == date.getMonth() && payday_arr[0] < date.getDate()) {
                            style.push('background-color: #f7b7b7');
                        }
                    } else if (parseFloat(row.unpaid) < 0){
                        style.push('background-color: #cdf7b7');
                    }
                }
            }

            if (row.active == false) {
                style.push('text-decoration: line-through');
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        columns: [
            [{
                field: 'c_num',
                title: '<b>Договор</b>',
                sortable: true,
                width: 100,
            }, {
                field: 'nm_usage_rights',
                title: '<b>Тип</b>',
                sortable: true,
                width: 100,
            }, {
                field: 'subleasing_farm_name',
                title: '<b>Наемодател</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'start_date',
                title: '<b>Дата на влизане в сила</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'due_date',
                title: '<b>Крайна дата</b>',
                align: 'center',
                width: 100,
            }, {
                field: 'owner_names',
                title: '<b>Наемател/Арендатор</b>',
                align: 'center',
                sortable: true,
                width: 150,
            }, {
                field: 'payday',
                title: '<b>Дата на падеж на плащането</b>',
                align: 'center',
            }, {
                field: 'renta_txt',
                title: '<b>Дължима рента в пари на декар</b>',
                align: 'center'
            }, {
                field: 'contract_area',
                title: '<b>Площ по договор (дка.)</b>',
                align: 'center'
            }, {
                field: 'rent_area',
                title: '<b>Площ за рента</b>',
                align: 'center'
            }, {
                field: 'money_to_collect_txt',
                title: '<b>Сума за получаване</b>',
                align: 'center'
            }, {
                field: 'collected_money_txt',
                title: '<b>Общо получени до момента</b>',
                align: 'center',
            }, {
                field: 'unpaid_txt',
                title: '<b>Остатък</b>',
                align: 'center'
            }]
        ],
        toolbar: '#collections-contracts-toolbar',
        pagination: false,
        onBeforeLoad: function() {},
        onLoadSuccess: function(data) {
            if (data.total !== undefined && data.total === 0) {
                return jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }
            if(selectedContractRowId !== undefined) {
                jQuery('#collection-contracts-table').datagrid('selectRecord', selectedContractRowId);
            } else{
                jQuery('#collection-contracts-table').datagrid('selectRow', 0);
            }
        },
        onSelect: function(index, row) {
            initCollectionPaymentsGrid(row);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

var selectedPersonalUseRowIndex;
function initCollectTreatments(rpcParams = {}) {
    jQuery('#collect-treatments-tables').datagrid({
        url: 'index.php?collections-rpc=collections-contracts-grid',
        rpcMethod: 'getCollectionsPersonalUse',
        nowrap: true,
        rownumbers: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        idField: 'id',
        sortName: 'c.c_num',
        border: true,
        sortOrder: 'asc',
        rpcParams: [rpcParams],
        columns: [
            [{
                field: 'c_num',
                title: '<b>Договор</b>',
                width: 100,
            },{
                field: 'farming_name',
                title: '<b>Стопанство</b>',
                width: 100,
            },{
                field: 'farming_mol',
                title: '<b>МОЛ</b>',
                align: 'center',
                width: 100,
            }, {
                field: 'start_date',
                title: '<b>Начална дата</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'due_date',
                title: '<b>Крайна дата</b>',
                align: 'center',
                width: 100,
            }, {
                field: 'owner_names',
                title: '<b>Собственик/длъжник</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'egn_eik',
                title: '<b>ЕГН/ЕИК</b>',
                align: 'center',
                sortable: true,
                width: 150,
            }, {
                field: 'renta_type_name',
                title: '<b>Тип натура</b>',
                align: 'center',
                width: 150,
            }, {
                field: 'personal_use_area',
                title: '<b>Площ за лично ползване</b>',
                align: 'center',
                formatter: function(value,row,index){
                    if(value){
                        return parseFloat(value).toFixed(3);
                    }
                }
            }, {
                field: 'personal_use_treatments_sum_txt',
                title: '<b>Сума за обработки</b>',
                align: 'center',
            }, {
                field: 'personal_use_paid_treatments_txt',
                title: '<b>Платени обработки</b>',
                align: 'center',
            }, {
                field: 'personal_use_unpaid_treatments_txt',
                title: '<b>Остатък за получаване</b>',
                align: 'center',
            }]
        ],
        toolbar: '#collections-personal-use-toolbar',
        pagination: false,
        onBeforeLoad: function() {},
        onLoadSuccess: function(data) {
            if (data.total !== undefined && data.total === 0) {
                return jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }
            if(selectedPersonalUseRowIndex !== undefined) {
                jQuery('#collect-treatments-tables').datagrid('selectRow', selectedPersonalUseRowIndex);
                let row = jQuery('#collect-treatments-tables').datagrid('getSelected');
                initCollectionPersonalUseGrid(row);
            } else{
                jQuery('#collect-treatments-tables').datagrid('selectRow', 0);
            }
            let GET = getQueryParams();
            if(GET.c_id  && GET.fy){
                restorePageURL(GET);
                jQuery('#search-personal-use-c-id').val('');
            }
        },
        onSelect: function(index, row) {
            selectedPersonalUseRowIndex = index;
            initCollectionPersonalUseGrid(row);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}