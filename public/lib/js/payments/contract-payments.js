var payment_reduce = false;
var payment_type = 'rent';

function initContractPaymentsGrid(contract_id, annex_id, year) {

	if(annex_id == undefined) {
		annex_id = 0;
	}

	let firstselected = false;
    
	//change URL without refresh if it's possible
	jQuery('#contract-payments-tables').treegrid({
		nowrap: true,
		singleSelect: false,
		title: 'Ренти',
		iconCls: 'icon-rents',
		pageSize: 20,
		fit: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?payments-rpc=contract-payments-grid',
		rpcParams: [contract_id, annex_id, year],
		sortName: 'owner_id',
		sortOrder: 'asc',
		idField: 'id',
		treeField: 'owner_names',
		selectOnCheck: true,
		checkOnSelect: true,
		columns: [[
			{
				field: 'for_payment_checkbox',
				title: '<b>За <br/>Информация</b>',
				sortable: false,
				width: 70,
				align: 'center',
				rowspan: 2,
				checkbox: true,
				formatter: function(value, row, index) {

					return '<input data-row-index="' + index + '" data-owner-id="'+row.owner_id+'" data-charged-renta="'+row.charged_renta+'" data-contract-id="'+row.contract_id+'" data-path="'+row.path+'" data-is-heritor="'+row.is_heritor+'" type="checkbox"/>';
				}
			},{
				title: '<b>Информация за собственик</b>',
				colspan: 2
			}, {
				field: 'all_owner_area',
				title: '<b>Притежавана<br/>площ<br/>(дка)</b>',
				sortable: false,
				align: 'center',
				rowspan: 2,
				width: 90,
				formatter: function(value, row, index) {
					if (row.all_owner_no_rounded && row.all_owner_no_rounded.length > row.all_owner_area.length) {
						return setTootip('Цяла стойност: ' + row.all_owner_no_rounded, row.all_owner_area);
					}
					return row.all_owner_area;

				}
			}, {
				field: 'pu_area',
				title: '<b>Площ за<br/>лично<br/>ползване (дка)</b>',
				sortable: false,
				align: 'center',
				rowspan: 2,
				width: 95,
				formatter: function(value, row, index) {
					if(row.pu_area === undefined || row.pu_area == ''){
						return '-';
					} else {
						return row.pu_area;
					}
				}
			}, {
				field: 'owner_area',
				title: '<b>Използвана<br/>площ<br/>(дка)</b>',
				sortable: false,
				align: 'center',
				rowspan: 2,
				width: 80
			}, {
				title: '<b>Информация за рента в лева</b>',
				colspan: 6
			},{
				title: '<b>Информация за рента в натура</b>',
				colspan: 9
			}
			// ,{
			// 	title: '<b>Информация за Общо платена рента</b>',
			// 	colspan: 2
			// 	}
			,{
				title: '<b>Информация за лично ползване</b>',
				colspan: 7
			}
			],
		[{
				field: 'owner_names',
				title: '<b>Име</b>',
				sortable: false,
				nowrap: false,
				width: 250,
				styler: function(value, row, index) {
					if (!row.is_heritor) {
						return 'font-weight: bold;';
					}
				}
			}, {
				field: 'rep_names',
				title: '<b>Представител</b>',
				sortable: false,
				width: 150
			},{
				field: 'renta_txt',
				title: '<b>Сума по<br/>договор</b>',
				sortable: false,
				width: 100,
				align: 'center'
			}, {
				field: 'charged_renta_txt',
				title: '<b>Начислена<br/>сума</b>',
				sortable: false,
				width: 100,
				align: 'center',
				formatter: function(value) {
					if(value == null){
						return '-';
					}
					return value;
				}
			}, {
				field: 'paid_renta_txt',
				title: '<b>Изплатена<br/>сума</b>',
				sortable: false,
				width: 100,
				align: 'center',
				styler: function(value, row, index) {
					if (parseFloat(value) < 0) {
						return 'background-color:red;';
					}
				}
			},
			{
				field: 'paid_renta_by',
				title: '<b>чрез</b>',
				align: 'center',
				sortable: false,
				width: 145
			}, {
				field: 'unpaid_renta_txt',
				title: '<b>Оставаща<br/>сума</b>',
				sortable: false,
				width: 100,
				align: 'center',
				formatter: function(value, row, index) {
					if(row.personal_use_price_sum && row.personal_use_price_sum > 0){
						return '<span style="text-decoration: underline" title="Собственикът дължи ' + row.personal_use_price_sum + ' лв. за обработки." class="easyui-tooltip">' + parseFloat(value).toFixed(2) + '</span>';
					} else {
						return value;
					}
				}
			},
			{
				field: 'over_paid_txt',
				title: '<b>Надплатена<br/>сума</b>',
				align: 'center',
				sortable: false,
				width: 100
			},{
				field: 'renta_nat_type',
				title: '<b>Тип натура</b>',
				sortable: false,
				width: 150,
				align: 'center'
			},{
				field: 'renta_nat_text',
				title: '<b>Количество<br/>по договор</b>',
				sortable: false,
				width: 100,
				align: 'center'
			}, {
				field: 'charged_renta_nat_text',
				title: '<b>Начислено<br/>количество</b>',
				sortable: false,
				width: 100,
				align: 'center'
			}, {
				field: 'paid_renta_nat',
				title: '<b>Изплатено<br/>количество</b>',
				sortable: false,
				width: 100,
				align: 'center'
			},
			{
				field: 'paid_renta_nat_by',
				title: '<b>чрез</b>',
				align: 'center',
				sortable: false,
				width: 200
			}
			,{
				field: 'paid_renta_nat_by_detailed',
				title: '<b>детайлно</b>',
				align: 'center',
				sortable: false,
				width: 145
			}
			, {
				field: 'unpaid_renta_nat',
				title: '<b>Оставащо<br/>количество</b>',
				sortable: false,
				width: 100,
				align: 'center',
				formatter: function(value,row,index){
					if(value && !isNaN(value)){
						return parseFloat(value).toFixed(3);
					} else {
						return value;
					}
				}
			},
			{
				field: 'unpaid_renta_nat_unit_value',
				title: '<b>в пари (ед. ст.)</b>',
				align: 'center',
				sortable: false,
				width: 100,
				formatter: function(value,row,index){
					if(value && !isNaN(value)){
						return parseFloat(value).toFixed(2);
					} else {
						return value;
					}
				}
			},
			{
				field: 'over_paid_nat',
				title: '<b>Надплатена натура</b>',
				align: 'center',
				sortable: false,
				width: 145,
				formatter: function(value,row,index){
					if(value && !isNaN(value)){
						return parseFloat(value).toFixed(2);
					} else {
						return value;
					}
				}
			},
			{
				field: 'personal_use_nat_types_names',
				title: '<b>Тип натура<br>за лично ползване</b>',
				sortable: false,
				width: 120,
				align: 'center'
			},
			{
				field: 'personal_use_renta',
				title: '<b>Дължимо<br>количество</b>',
				sortable: false,
				width: 100,
				align: 'center'
			},
			{
				field: 'personal_use_paid_renta',
				title: '<b>Изплатено<br>количество</b>',
				sortable: false,
				width: 100,
				align: 'center'
			},
			{
				field: 'personal_use_unpaid_renta',
				title: '<b>Оставащо<br>количество</b>',
				sortable: false,
				width: 100,
				align: 'center'
			},
			{
				field: 'personal_use_treatments_sum',
				title: '<b>Сума за<br>обработки (лв.)</b>',
				sortable: false,
				width: 110,
				align: 'center'
			},
			{
				field: 'personal_use_paid_treatments',
				title: '<b>Изплатени сума <br/>за обработки (лв.)</b>',
				sortable: false,
				width: 110,
				align: 'center'
			},
			{
				field: 'personal_use_unpaid_treatments',
				title: '<b>Остатъчна сума <br/>за обработки (лв.)</b>',
				sortable: false,
				width: 110,
				align: 'center'
			}
		]],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btnpayrenta',
				text: 'Изплащане на рента',
				iconCls: 'icon-payments',
				handler: function() {
					var getSelections = jQuery('#contract-payments-tables').treegrid('getSelections');
					let ownersTree = jQuery('#contract-payments-tables').treegrid('getData');
					for(var i = 0; i < getSelections.length; i++) {
						if(checkHaveChildrenPaids(ownersTree, getSelections[i].owner_id) == true) {
							jQuery.messager.alert('Грешка', 'Не може да изплатите рента на починалият собственик след като има плащане към наследниците му.', 'warning');
							return;
						}
					}

					var farmingId = jQuery('#contracts-tree').tree('getSelected').attributes.farming_id;
					let farmingInfo = {};
					if (getSelections[0]) {
						payment_type = 'rent';
						let pko_exists = '';
						jQuery('#pko_exists').hide();
						
						if(getSelections.length == 1){
							if(getSelections[0].unpaid_renta > 0 && getSelections[0].personal_use_price_sum && getSelections[0].personal_use_unpaid_treatments_arr && (getSelections[0].personal_use_price_sum && getSelections[0].personal_use_unpaid_treatments_arr.reduce((partialSum, a) => partialSum + parseFloat(a), 0) > 0)) {
								let unpaid_personal_use_treatments = getSelections[0].personal_use_unpaid_treatments_arr.reduce((partialSum, a) => partialSum + parseFloat(a), 0);
								for(let i = 0; i < ComboboxData.FarmingCombobox.length; i++) {
									if(farmingId === ComboboxData.FarmingCombobox[i].id){
										farmingInfo = ComboboxData.FarmingCombobox[i]; break;
									}
								}
								jQuery('#collections_recipient_name').val(farmingInfo.mol);
								jQuery('#collections_recipient_address').val(farmingInfo.address);
								jQuery('#collections_recipient_egn').val(farmingInfo.mol_egn);
								jQuery('#collections_recipient_lk').val('');
								jQuery('#pko_exists').show();
								pko_exists = '<p>Избраният собственик дължи <span style="font-weight: bold">' + unpaid_personal_use_treatments + ' лв.</span> за обработки.<br>Ако желаете да се създаде ПКО за обработките и сумата да бъде приспадната от рентата, натиснете бутона <span style="font-weight: bold">Изплати чрез приспадане</span>.<br>Операцията ще създаде РКО за изплатена рента и ПКО за получена сума за обработки.</p>\n' +
									'\t\t\t\t<p style="font-size: 14px; margin-top: 15px; text-align: center">Дължима сума след приспадане:<span style="font-weight: bold"><span id="sumAfterDeduction">'+ (parseFloat(getSelections[0].unpaid_renta - unpaid_personal_use_treatments).toFixed(2)) +'</span> лв.</span></p>';
							}
							jQuery('#pko_exists > div').html(pko_exists);
							jQuery('#paymentQtyOptions').show();
						}

						//payment request type is for single payment
						//used on payment validation
						paymentType = 'single';
						payment_reduce = false;

						jQuery('#win-add-payment').window('setTitle', 'Изплащане на рента');
						jQuery('#btn-add-payment > a').linkbutton({text:'Изплати'});

						initPaymentFromMoneyPanel(farmingId);

						jQuery('#win-add-payment').window('resize', {
							height: 690
						});

						jQuery('#win-add-payment').window('open');
						jQuery('#payment-subjects-text').val('');
					} else {
						jQuery.messager.alert('Грешка', 'Не е избран собственик!', 'error');
					}
				}
			},{
				id: 'btnaddnatpayment',
				text: 'Изплащане на натура',
				iconCls: 'icon-payments',
				handler: function() {
					var getSelections = jQuery('#contract-payments-tables').treegrid('getSelections');

					for(var i = 0; i < getSelections.length; i++) {
						if(getSelections[i].have_children_paids && getSelections[i].have_children_paids == true) {
							jQuery.messager.alert('Грешка', 'Не може да изплатите рента на починалият собственик след като има плащане към наследниците му.', 'warning');
							return;
						}
					}

					if (getSelections[0]) {
						payment_type = 'rent';
						jQuery('#paymentQtyOptions').show();
						
						//check if renta nat type is the same for all selected rows
						var renta_flag = true;

						var main_selection_index = 0;
						for (var i = 0; i < getSelections.length; i++)
						{
							if(Object.keys(getSelections[i]['renta_nat']).length != 0){
								main_selection_index = i;
								break;
							}
							else
							{
								jQuery.messager.alert('Грешка', 'Селектираният договор няма рента в натура!', 'error');
								return;
							}
						}
						for (var i = 0; i < getSelections.length; i++)
							{
								for (var k = 0; k < getSelections[main_selection_index]['nat_type_ids'].length; k++){
									if (getSelections[main_selection_index]['nat_type_ids'][k] != getSelections[i]['nat_type_ids'][k]) {
										renta_flag = false;
										break;
									}
								}
							}

						if (renta_flag) {
							//payment type shows if single contract payment is requested or multi
							//used on validation
							paymentType = 'single';
							payment_reduce = false;
							jQuery('#win-add-nat-payment').window('setTitle', 'Изплащане на натура');
							jQuery('#btn-add-nat-payment > a').linkbutton({text:'Изплати'});

							clearNaturaPayment();
                            var farmingId = jQuery('#contracts-tree').tree('getSelected').attributes.farming_id;
							initPaymentFromNaturaPanel(farmingId);
							jQuery('#win-add-nat-payment').window('open');
						} else {
							jQuery.messager.alert('Грешка', 'На селектирания получател не се дължи рента в натура!', 'error');
						}
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете собственик!', 'error');
					}
				}
			}, {
				id: 'btnaddpersonaluse',
				text: 'Изплащане на лично ползване',
				iconCls: 'icon-payments',
				handler: function() {
					var getSelections = jQuery('#contract-payments-tables').treegrid('getSelections');
				if (getSelections.length === 0) {
					jQuery.messager.alert('Грешка', 'Моля изберете собственик!', 'error');
				}

				if(getSelections.length > 1){
					jQuery.messager.alert('Грешка', 'Личното ползване се изплаща само по един собственик!', 'error');
					return;
				}

				if(getSelections[0]['personal_use'] === undefined){
					jQuery.messager.alert('Грешка', 'Селектираният собственик няма лично ползване!', 'error');
					return;
				}

				payment_type = 'personal_use';
				jQuery('#paymentQtyOptions').hide();

				clearNaturaPayment();

				jQuery('#win-add-nat-payment').window('setTitle', 'Изплащане на лично ползване');
				jQuery('#btn-add-nat-payment > a').linkbutton({text:'Изплати'});

				var farmingId = jQuery('#contracts-tree').tree('getSelected').attributes.farming_id;
				initPaymentFromNaturaPanel(farmingId, 'personal_use');

				jQuery('.add-payment-charged-renta-label').hide();
				jQuery('.add-payment-charged-renta-value').hide();

				jQuery('#addNatPaymentBeginningMarker > legend').text("Информация за изплащане на лично ползване");
				jQuery('#paymentQtyFields > legend').text("Изплащане на лично ползване");

				checkRentaNature(400);

				jQuery('#win-add-nat-payment').window('open');
				}
			}, {
                id:'btnviewinfo',
                text:'Информация',
                iconCls:'icon-info',
                handler:function(){
                    var getChecked = jQuery('#contract-payments-tables').datagrid('getChecked');

                    if(getChecked.length > 0) {

                        var url_string = '&owner_ids=';
                        for(i=0; i<getChecked.length; i++) {
                            if(i == (getChecked.length - 1)) {
                                url_string += getChecked[i].owner_id+'';
                            }else {
                                url_string += getChecked[i].owner_id+',';
                            }
                        }
                        window.open("index.php?page=Owners.Home" + url_string, '_blank');

                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.', 'error');
                    }
                }
            }],
		onLoadSuccess: function (row, data) {
			var contractsTreeFilterObj = jQuery('#contracts-tree').data().tree.options.rpcParams[0];
			var selected = getSelectedOwnerByFilter(data.rows, contractsTreeFilterObj, false);

			if(!selected && data.rows.length == 1 && (data.rows[0].is_dead == false)) {
				selected = data.rows[0];
			}

			if(!selected && data.rows.length == 1 && data.rows[0].is_dead == true && data.rows[0].children?.length == 1) {
				selected = data.rows[0].children[0];
			}
			if(selected) {
				firstselected = true;
				jQuery(this).treegrid('select', selected.id);
			}
			firstselected = false;

			//Forget to select all rows.. this is necessary because you cannot select a parent and child at the same time
			jQuery(this).closest('.datagrid-view').find('.datagrid-header-check input[type="checkbox"]').hide();
		},
		onBeforeLoad: function() {
			jQuery('#contract-payments-tables').treegrid('clearChecked');
			if(contract_id == 0){
				return false;
			}	
		},
		onSelect: function(node) {
			if (node.allow_owner_payment == true) {
				unselectRelatedNodes(node);
			}
			
			if(node.is_dead && node.allow_owner_payment == false) {
				jQuery('#contract-payments-tables').treegrid('unselect', node.id);
			}

			enableDisablePaidRentBtn(!firstselected);

			firstselected = false;
		},
		onUnselect: function(node) {
			enableDisablePaidRentBtn();
		},
		onUnselectAll: function() {
			enableDisablePaidRentBtn();
		},
		onSelectAll: function() {
			var selections = jQuery('#contract-payments-tables').treegrid('getSelections');
			for(var i=0; i < selections.length; i++) {
				if(selections[i].is_dead && elections[i].allow_owner_payment == false) {
					jQuery('#contract-payments-tables').treegrid('unselect', selections[i].id);
				}
			}
			enableDisablePaidRentBtn();
			firstselected = false;
		},
        rowStyler: function (row) {
            if (row && row.is_dead && row.allow_owner_payment == false) {
                return 'color:#aaa;';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});


	function checkHaveChildrenPaids(obj, ownerId) {
		if (Array.isArray(obj)) {
			return obj.some(item => checkHaveChildrenPaids(item, ownerId));
		} else if (typeof obj === 'object' && obj !== null) {
			if (obj.owner_id === ownerId && obj.have_children_paids === true) {
				return true;
			}
			return Object.values(obj).some(value => checkHaveChildrenPaids(value, ownerId));
		}
		return false;
	}

	function unselectRelatedNodes(node)
	{
		const children = jQuery('#contract-payments-tables').treegrid('getChildren',node.id);

		if (children.length) {
			unselectChildNodes(children)
		}

		if (node.level > 1) {
			unselectAllParentNodes(node);
		}
	}

	function unselectChildNodes(children)
	{
		children.forEach((child) => {
			//should refresh selction nodes on every iterration
			const selectedNodes = jQuery('#contract-payments-tables').treegrid('getSelections');
			const selectedIds = selectedNodes.map(node => node.id);

			if (selectedIds.includes(child.id)) {
				jQuery('#contract-payments-tables').treegrid('unselect',child.id);
				//posible recursion
				unselectChildNodes([child]);
			}			
		});		
	}

	function unselectAllParentNodes(node)
	{
		let parentLevel = node.level - 1;
		
		while (parentLevel > 0) {
			//should refresh selction nodes on every iterration
			const selectedNodes = jQuery('#contract-payments-tables').treegrid('getSelections');
			const parentNodes = filterNodesByLevel(parentLevel, selectedNodes);

			parentNodes.forEach((parent) => {
				const children = jQuery('#contract-payments-tables').treegrid('getChildren',parent.id);
				if (children.length) {
					children.forEach((child) => {
						if (child.id == node.id) {
							jQuery('#contract-payments-tables').treegrid('unselect',parent.id);
						}
					});
				}
			})
			parentLevel --;
		}
	}
	
	function filterNodesByLevel(level, selectedNodes)
	{
		return selectedNodes.filter(node => node.level == level);
	}

	function enableDisablePaidRentBtn(showMsg = true) {
		var selections = jQuery('#contract-payments-tables').datagrid('getSelections');
		jQuery('#btnpayrenta').linkbutton({disabled:false});
		jQuery('#btnaddnatpayment').linkbutton({disabled:false});
		jQuery('#btnaddpersonaluse').linkbutton({disabled:false});
		if(selections.length === 1) {
			if(selections[0].personal_use_renta_arr) {
				var hasOwnerWithoutPersonalUse = false;
				jQuery.each(selections[0].personal_use_renta_arr, function(key, value) {
					if(value == 0){
						hasOwnerWithoutPersonalUse = true;
					}
					});

					if(hasOwnerWithoutPersonalUse) {
						jQuery('#btnaddpersonaluse').linkbutton({disabled:true});
					}
			} else {
				jQuery('#btnaddpersonaluse').linkbutton({disabled:true});
			}
			
			return false;
		}

		if(selections.length > 1) {
			jQuery('#btnaddpersonaluse').linkbutton({disabled:true});
		}

		var hasSelectedOwners = false;
		var hasOwnerWithoutUnpaidMoney = false;
		var hasOwnerWithoutUnpaidNature = false;
		for (var owner of selections){
			hasSelectedOwners = true;
			let hasTheOwnerUnpaidNature = false;

			if(owner.unpaid_renta_nat_arr) {
				jQuery.each(owner.unpaid_renta_nat_arr, function(key, value) {
					if(value > 0){
						hasTheOwnerUnpaidNature = true;
					}
					});
			}
			if(parseFloat(owner.unpaid_renta) == 0.00) {
				hasOwnerWithoutUnpaidMoney = true;
			}
			if(!hasTheOwnerUnpaidNature) {
				hasOwnerWithoutUnpaidNature = true;
			}
		}

		if(hasSelectedOwners && showMsg) {
			if(hasOwnerWithoutUnpaidMoney) {
				jQuery('#btnpayrenta').linkbutton({disabled:true});
			}
			if(hasOwnerWithoutUnpaidNature) {
				jQuery('#btnaddnatpayment').linkbutton({disabled:true});
			}

			if(hasOwnerWithoutUnpaidNature && hasOwnerWithoutUnpaidMoney) {
				jQuery.messager.alert('Предупреждение', 'Избрали сте получател, който няма оставаща сума и оставащо количество.', 'warning');
				return;
			} else if(hasOwnerWithoutUnpaidNature){
				jQuery.messager.alert('Предупреждение', 'Избрали сте получател, който няма оставащо количество.', 'warning');
				return;
			} else if(hasOwnerWithoutUnpaidMoney) {
				jQuery.messager.alert('Предупреждение', 'Избрали сте получател, който няма оставаща сума.', 'warning');
				return;
			}
		}
	}

	function getSelectedOwnerByFilter(data, filter, selected = false) {
		if(!filter) return false;
		if(selected !== false) return selected;

		for (var i = 0; i < data.length; i++) {
			if (
				(!!filter.person_name && (!!data[i].owner_names && data[i].owner_names.toLowerCase().includes(filter.person_name.toLowerCase()) || !!data[i].rep_names && data[i].rep_names.toLowerCase().includes(filter.person_name.toLowerCase()))) ||
				(!!filter.person_egn && (!!data[i].egn_eik && data[i].egn_eik.includes(filter.person_egn) ||
					!!data[i].rep_egn && data[i].rep_egn == filter.person_egn)) ||
				(!!filter.owner_name && !!data[i].owner_names && data[i].level === 1 && !data[i].is_dead && data[i].owner_names.toLowerCase().includes(filter.owner_name.toLowerCase())) || (!!filter.owner_egn && !!data[i].owner_egn  && data[i].level === 1 && !data[i].is_dead && data[i].owner_egn.includes(filter.owner_egn)) ||
				(!!filter.heritor_name && !!data[i].owner_names && data[i].level > 1 && !data[i].is_dead && data[i].owner_names.toLowerCase().includes(filter.heritor_name.toLowerCase())) || (!!filter.heritor_egn && !!data[i].owner_egn && data[i].level > 1 && !data[i].is_dead && data[i].owner_egn.includes(filter.heritor_egn)) ||
				(!!filter.rep_name && !!data[i].rep_names && data[i].rep_names.toLowerCase().includes(filter.rep_name.toLowerCase())) || (!!filter.rep_egn && !!data[i].rep_egn && JSON.parse(data[i].rep_egn).includes(filter.rep_egn)) ||
				(!!filter.company_name && !!data[i].owner_names && data[i].owner_names.toLowerCase().includes(filter.company_name.toLowerCase())) || (!!filter.company_eik && !!data[i].owner_egn && data[i].owner_egn.includes(filter.company_eik))
			) {
				selected = data[i];
				return selected;
			}
			if (data[i].children) {
				selected = getSelectedOwnerByFilter(data[i]['children'], filter, selected);
				if(selected !== false) return selected;
			}
		}

		return selected;
	}
}
