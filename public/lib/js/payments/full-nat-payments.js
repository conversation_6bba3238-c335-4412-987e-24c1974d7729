function initPaymentFromNaturaPanel(farmingIdStr, type = 'payment')
{
    clearRkoNatNumbers();
	jQuery('#addNatPaymentBeginningMarker > legend').text("Информация за рента в натура");
	jQuery('#paymentQtyFields > legend').text("Възможности за изплащане на рента в натура");
	jQuery('.add-payment-charged-renta-label').show();
	jQuery('.add-payment-charged-renta-value').show();

	var date = new Date();
	var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
	var paymentsTable = jQuery('#contract-payments-tables');
	var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');

	var searchYearText = '';
	var c_num = '';

	//calculate required variables
	var by_contract = [],
			charged = [],
			paid = [],
			unpaid = [],
			renta_nat_type = '';

	arr_by_contract = [];
	arr_paid_renta_nat = [];
	arr_charged_renta_nat = [];
	arr_unpaid_renta_nat = [];
	var getChecked;

	if(paymentsTable.length) {
		getChecked = paymentsTable.treegrid('getSelections');
		c_num = jQuery('#contracts-tree').tree('getSelected').attributes.c_num;
		searchYearText = jQuery('#search-year').combobox('getText');
	}
 	else if (jQuery('#personal-use-report-tables').length) {
		getChecked = [jQuery('#personal-use-report-tables').treegrid('getSelected')];
		searchYearText = jQuery('#pu-collections-search-year').combobox('getText');
		c_num = '';
 	}
	else {
		getChecked = ownerPaymentsTable.datagrid('getSelections');
		var selectedOwner = jQuery('#owners-contracts-tree').tree('getSelected');
		c_num = jQuery('#contracts-owner-payments-tables').datagrid('getSelections')[0].c_num;
		searchYearText = jQuery('#search-year').combobox('getText');

		if (selectedOwner != undefined) {

			if (selectedOwner.attributes.is_dead == true) {
				jQuery('#dead-owner-message-nat').show();
			} else {
				jQuery('#dead-owner-message-nat').hide();
			}
		}
	}

	if(type !== 'personal_use') {
		jQuery('#np-method-cash-radio').prop('checked',true);
		jQuery('#np-method-cash-radio').trigger('change');
		jQuery('#generate-natura-payment-order').prop('checked', true);
	}

	var renta_nat_types = [];
	var personal_use = [];
	let showDeadMsg = false;

	for (var i = 0; i < getChecked.length; i++)
	{
		if(type === 'personal_use') {
				renta_nat_types = getChecked[0].personal_use_nat_types_names_arr;
				by_contract = getChecked[0].personal_use_renta_arr;
				paid = getChecked[0].personal_use_paid_renta_arr ?? [0];
				unpaid = getChecked[0].personal_use_unpaid_renta_arr ?? [0];
				charged = [0];
		} else {
			renta_nat_types = getChecked[0].renta_nat_type.toString().split('<br>');

			arr_by_contract = getChecked[i]['renta_nat_text'].toString().split('<br>');
			if(getChecked[i]['paid_renta_nat_text'] != null){
				arr_paid_renta_nat = getChecked[i]['paid_renta_nat_text'].toString().split('<br>');
			}
			arr_charged_renta_nat = getChecked[i]['charged_renta_nat_text'].toString().split('<br>');
			arr_unpaid_renta_nat = getChecked[i]['unpaid_renta_nat_text'].toString().split('<br>');

			for (var j = 0; j < renta_nat_types.length; j++) {
				if(typeof(by_contract[j]) == 'undefined' || isNaN(by_contract[j])) {
					by_contract[j] = 0;
				}
				if(arr_by_contract[j] != '' && typeof(arr_by_contract[j]) != 'undefined' && !isNaN(arr_by_contract[j])){
					by_contract[j] += parseFloat(arr_by_contract[j]);
				}
				if(typeof(paid[j]) == 'undefined') {
					paid[j] = 0;
				}
				if(arr_paid_renta_nat[j] != '' && typeof(arr_paid_renta_nat[j]) != 'undefined' && !isNaN(arr_paid_renta_nat[j])){
					paid[j] += parseFloat(arr_paid_renta_nat[j]);
				}
				if(typeof(charged[j]) == 'undefined' || isNaN(arr_charged_renta_nat[j])) {
					charged[j] = 0;
				}
				if(arr_charged_renta_nat[j] != '' && typeof(arr_charged_renta_nat[j]) != 'undefined' && !isNaN(arr_charged_renta_nat[j])){
					charged[j] += parseFloat(arr_charged_renta_nat[j]);
				}
				if(typeof(unpaid[j]) == 'undefined') {
					unpaid[j] = 0;
				}
				if(arr_unpaid_renta_nat[j] != '' && typeof(arr_unpaid_renta_nat[j]) != 'undefined'){
					unpaid[j] += parseFloat(arr_unpaid_renta_nat[j]);
				}
			}
		}

		if(getChecked[i].hasOwnProperty('is_dead')  && getChecked[i]['is_dead'] == true) {
            showDeadMsg = true;
        }
	}

	if (showDeadMsg == true) {
		jQuery('#dead-owner-message-nat').show();
	} else {
		jQuery('#dead-owner-message-nat').hide();
	}

	var renta_types = getChecked[0].renta_nat_info;

	let personalUseCropCount = 0;
	for (var i = 0; i < renta_nat_types.length; i++) {
		if(renta_nat_types[i] == '') {
			continue;
		}

		jQuery('#js-natura-renta-payment-info').append('' +
			'<tr>' +
			'<td style="text-align: right;">' + renta_nat_types[i] + '</td>' +
			'<td style="text-align: right;">' + ( (by_contract[i]) ? parseFloat(by_contract[i]).toFixed(3) : ' Няма' ) + '</td>'+
			'<td style="text-align: right;" class="add-payment-charged-renta-value">' + parseFloat(charged[i]).toFixed(3) + '</td>'+
			'<td style="text-align: right;">' + (jQuery.isNumeric(paid[i]) ? parseFloat(paid[i]).toFixed(3) : '0.000') +  '</td>'+
			'<td style="text-align: right;">' + (jQuery.isNumeric(unpaid[i]) ? parseFloat(unpaid[i]).toFixed(3) : '0.000') + '<br/>'+
			'</tr>'
		);

		if(!jQuery.isNumeric(by_contract[i])) {
			continue;
		}
		personalUseCropCount++;

		jQuery('#js-renta-nat-leva-payment').append('' +
			'<tr>' +
			'	<td style="text-align: right;">' + renta_nat_types[i] + '</td>' +
				'<td><input id="renta-nat-price-per-unit-'+ i +'"></td>' +
				'<td><input id="renta-nat-total-'+ i +'"></td>' +
			'</tr>'
		);

		jQuery('#js-renta-nat-nat-payment').append('' +
			'<tr>' +
			'	<td style="text-align: right;">' + renta_nat_types[i] + '</td>' +
				'<td><input id="renta-nat-paid-amount-'+ i +'"></td>' +
			'</tr>');


		var renta_type_id = getChecked[0].renta_nat_type_id[i];
		var renta_type_unit_value = 0;

		jQuery.each(renta_types, function(key, value) {
		   if(value.unit_value && key == renta_type_id) {
		   	  renta_type_unit_value = value.unit_value;
		   	  return;
		   }
		});

		var renta_nat_total_value = renta_type_unit_value * unpaid[i];

		jQuery('#renta-nat-price-per-unit-' + i).numberspinner({
			min: 0,
			width: 95,
            precision: 4,
            required: true,
            missingMessage: 'Това поле е задължително!',
            value: renta_type_unit_value,
            onChange: function(newValue, oldValue){
            	var renta_nat_unit_id = this.id.split("-");
            	var id = renta_nat_unit_id[renta_nat_unit_id.length - 1];
            	var renta_nat_total = jQuery('#renta-nat-total-' + id);

            	if(!renta_nat_total.data().hasOwnProperty('numberspinner')) {
            		return;
            	}

            	var renta_nat_total_val = renta_nat_total.numberspinner('getValue');
            	var unpaid_renta_by_natura_type = renta_nat_total_val / oldValue;
            	var renta_nat_total_new_val = unpaid_renta_by_natura_type * newValue;

            	renta_nat_total.numberspinner('setValue', renta_nat_total_new_val);
            }
		});

		jQuery('#renta-nat-total-'+i).numberspinner({
			min: 0,
			width: 95,
            precision: 3,
            required: true,
            missingMessage: 'Това поле е задължително!',
            value: roundTo(renta_nat_total_value, 3),
			onChange: () => {
				this.clearRkoNatNumbers();
			}
		});

		jQuery('#renta-nat-paid-amount-'+i).numberspinner({
			min: 0,
			width: 95,
			precision: 3,
			required: true,
			missingMessage: 'Това поле е задължително!',
			value: unpaid[i]
		});
	}

	if(personalUseCropCount === 0) {
		jQuery('#paymentQtyFields').hide();
	} else {
		jQuery('#paymentQtyFields').show();
	}

	jQuery('#np-renta-nat-charged').html(charged);
	jQuery('#np-renta-nat-paid').html(paid);
	jQuery('#np-renta-nat-unpaid').html(unpaid);
	jQuery('#np-renta-nat-type').html(renta_nat_type);

    jQuery('#natura-to-natura').hide();
    jQuery('#natura-to-money').show();
    jQuery('#js-renta-nat-leva-payment').show();
    jQuery('#js-renta-nat-nat-payment-div').hide();

    jQuery('#np-payment-subjects-text').val('Изплащане по договор ' + c_num +' за '  + searchYearText);

    jQuery('#np-payment-subjects-row').show();
    jQuery('#np-payment-subjects-text-row').hide();
	jQuery("#nat_rko_standart").prop('checked',true);

	jQuery("#representative-nat-payment-option").hide();
    jQuery("#representative-nat-payment-checkbox").prop('checked',false);

    jQuery('#win-add-nat-payment').window('resize', {
        height: 540 + (renta_nat_types.length -1)*43
    });

	if(getChecked.length >= 1 && selectedOwner === undefined) {
		fillPayByNatOwnerInfo(getChecked[0]);

		if(getChecked[0].rep_names && getChecked[0].rep_names.trim().length > 0) {
            jQuery("#representative-nat-payment-option").show();
            jQuery("#representative-nat-payment-checkbox").change(function () {
                if(jQuery(this).is(':checked')) {
                    fillPayByNatRepInfo(getChecked[0]);
                } else {
                    fillPayByNatOwnerInfo(getChecked[0]);
                }
            });
        }
	} else if(selectedOwner && ownerPaymentsTable.length) {
		fillPayByNatOwnerInfo(selectedOwner.attributes);

		if(selectedOwner.attributes.rep_names && selectedOwner.attributes.rep_names.trim().length > 0) {
            jQuery("#representative-nat-payment-option").show();
            jQuery("#representative-nat-payment-checkbox").change(function () {
                if(jQuery(this).is(':checked')) {
                    fillPayByNatRepInfo(selectedOwner.attributes);
                } else {
                    fillPayByNatOwnerInfo(selectedOwner.attributes);
                }
            });
        }
    }

	jQuery('#np-type-money > input').change(function() {
		if (jQuery('#np-type-money > input').is(':checked') == true) {
			checkRentaMoney(530);
		}
		showNpRkoTextField()
	});

	jQuery('#np-type-natura > input').change(function() {
		if (jQuery('#np-type-natura > input').is(':checked') == true) {
			checkRentaNature(380);
		}
		showNpRkoTextField();
	});



	jQuery('#np-bank-account-row').hide();
	jQuery('#np-orderer-bank-account-row').hide();

	jQuery('#np-method-cash > input').change(function() {
		if (jQuery('#np-method-cash > input').is(':checked') == true)
		{
			jQuery('label[for="generate-natura-payment-order"]').html("Генерирай разходен касов ордер");
			jQuery('#generate-natura-payment-order').prop('checked', true);
			jQuery('#np-bank-account-row').hide();
			jQuery('#np-payment-subjects-options-row').show();
			jQuery('#np-payment-subjects-row').show();
			jQuery('#np-weighing-note-checkbox').prop('checked', false);
            jQuery('#rkoNatNumberingFields').show();
			jQuery('#np-orderer-bank-account-row').hide();
            jQuery('#win-add-nat-payment').window('resize', {
				height: jQuery('#btn-add-nat-payment').offset().top - jQuery('#addNatPaymentBeginningMarker').offset().top + 140
			});
			jQuery("#nat-rko-type-row").show();
		}

		showNpRkoTextField();
	});

	jQuery('#np-method-bank > input').change(function() {
		if (jQuery('#np-method-bank > input').is(':checked') == true)
		{
            clearRkoNatNumbers();
			jQuery('label[for="generate-natura-payment-order"]').html("Генерирай платежно нареждане");
			jQuery('#generate-natura-payment-order').prop('checked', false);
			jQuery('#np-bank-account-row').show();
			showNpRkoTextField(true);
			jQuery('#np-payment-subjects-row').hide();
			jQuery('#rkoNatNumberingFields').hide();
			jQuery('#np-orderer-bank-account-row').show();
            jQuery('#win-add-nat-payment').window('resize', {
				height: jQuery('#btn-add-nat-payment').offset().top - jQuery('#addNatPaymentBeginningMarker').offset().top + 140
			});

			jQuery("#nat_rko_type_declaration_row").hide();
			jQuery("#nat-rko-type-row").hide();
			jQuery("#nat_rko_standart").prop('checked',true);
			jQuery("#nat_rko_type_declaration").prop('checked',false);
		}
	});

	jQuery('#np-unit-price > input, #np-amount > input').numberbox({
		min: 0,
		precision: 2,
		required: true,
		missingMessage: 'Това поле е задължително!'
	});

	jQuery('#np-date > input').datebox({
		required: true,
		missingMessage: 'Това поле е задължително!',
		value: todayDate,
		editable: false
	});

    jQuery('#np-orderer-bank-account > input').combobox({
        url: 'index.php?farming-rpc=farming-iban',
        valueField: 'value',
        textField: 'text',
        multiple: false,
        editable: false,
        rpcParams: [farmingIdStr],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#np-payment-subjects-combobox').combobox({
        url: 'index.php?common-rpc=payment-subjects-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: false,
        editable: false,
        rpcParams: [{
        	selected: true
        }],
		onChange: function(newValue, oldValue) {

			if (newValue === RKO_MANUAL_TEXT_OPTION) {
				showNpRkoTextField(true);
			} else {
				jQuery('#np-payment-subjects-text-row').hide();
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	if(jQuery('#generate-natura-payment-order').is(':checked')){
		jQuery('#nat-rko-type-row').show();
	} else {
		jQuery('#nat-rko-type-row').hide();
	}

	jQuery('#generate-natura-payment-order').change(function() {
        if (jQuery('#generate-natura-payment-order').is(':checked')) {
            jQuery('#nat-rko-type-row').show();
			jQuery("#nat_rko_standart").prop('checked',true);
        } else {
            jQuery('#nat-rko-type-row').hide();
        }
    });

}

function checkRentaNature(winHeight = 350) {
	jQuery('#generate-natura-payment-order').prop('checked', false);
	jQuery('#np-payment-subjects-options-row').hide();
	jQuery('#np-payment-subjects-row').hide();
	jQuery('#np-payment-subjects-text-row').hide();
	jQuery('#rkoNatNumberingFields').hide();
	jQuery('#natura-to-money').hide();
	jQuery('#natura-to-natura').show();
	jQuery('#js-renta-nat-leva-payment').hide();
	jQuery('#js-renta-nat-nat-payment-div').show();
	jQuery('#nat-rko-type-row').hide();
	jQuery('#np-weighing-note-checkbox').prop('checked', true);
	jQuery('#win-add-nat-payment').window('resize', {
		height: winHeight
	});
}
function checkRentaMoney(winHeight = 530) {
	jQuery('#natura-to-natura').hide();
	jQuery('#natura-to-money').show();
	jQuery('#nat-rko-type-row').show();
	jQuery("#nat_rko_standart").prop('checked',true);

	jQuery('#js-renta-nat-leva-payment').show();
	jQuery('#js-renta-nat-nat-payment-div').hide();
	jQuery('#np-weighing-note-checkbox').prop('checked', false);
	jQuery('#np-method-cash > input').trigger('change');
	jQuery('#np-method-bank > input').trigger('change');
	jQuery('#generate-natura-payment-order').prop('checked', true);
	jQuery('#win-add-nat-payment').window('resize', {
		height: winHeight
	});
}

/**
 *
 * @param force
 */
function showNpRkoTextField(force = false) {
	let validationPassed = false;
	if (force !== true) {
		const method_cash = jQuery('#np-method-cash > input').is(":checked");
		const rkoManualSelected   = parseInt(jQuery('#np-payment-subjects-combobox').combobox('getValue')) === RKO_MANUAL_TEXT_OPTION;

		if(method_cash && rkoManualSelected) {
			validationPassed = true;
		}
	}

	if ( force || validationPassed ) {
		jQuery('#np-payment-subjects-text-row').show();
	}
}

function validateNewNatPayment() {
	var owner_array = [];
	var object = {};
	var data = {};
	var i = 0;
	var paymentsTable = jQuery('#contract-payments-tables');
	var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');
    var ownersData = null;

    if (paymentsTable.length) {
		ownersData = paymentsTable.treegrid('getSelections');

		let contractData = jQuery('#contracts-tree').tree('getSelected');

		for (i = 0; i < ownersData.length; i++) {
			var obj = {};
			obj.owner_id = ownersData[i].owner_id;
			obj.charged_renta = ownersData[i].charged_renta;
			obj.contract_id = ownersData[i].contract_id;
			obj.annex_id = contractData.attributes.annex_id;
			obj.path = ownersData[i].path;
			obj.is_heritor = ownersData[i].is_heritor;
			obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
			obj.renta_nat = ownersData[i].renta_nat;
			obj.unpaid_renta_nat = ownersData[i].unpaid_renta_nat_arr;
			obj.charged_renta_nat_values = ownersData[i].charged_renta_nat;
			obj.paid_renta_nat = ownersData[i].paid_renta_nat_by_contract ? ownersData[i].paid_renta_nat_by_contract[ownersData[i].contract_id] : ownersData[i].paid_renta_nat_details ? ownersData[i].paid_renta_nat_details : [];
			obj.owner_area = ownersData[i].owner_area;
			obj.uuid = ownersData[i].uuid;
			owner_array.push(obj);
		}

	} else {
		ownersData = ownerPaymentsTable.datagrid('getSelections');

		for (i = 0; i < ownersData.length; i++) {
			var ownerIdNatura = ownersData[i].id_renta_natura;
			var ownerPathNatura = ownersData[i].path_renta_natura;
            var farmingId = ownersData[i].farming_id;
			let annex_id = ownersData[i].annex_id;
            //if exist owners
			if(ownerIdNatura != undefined) {
				//put in owner_array all users who are owners
				jQuery.each(ownerIdNatura, function(key, value) {
					var obj = {};
				    obj.owner_id = key;
				    obj.contract_id = value.contract_id;
					obj.annex_id = annex_id;
				    obj.owner_area = value.area;
				    obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
					obj.renta_nat = ownersData[i].renta_nat_text;
				    obj.unpaid_renta_nat = value.unpaid_renta_nat;
				    obj.is_heritor = false;
				    obj.farming_id = farmingId;
					obj.uuid = value.uuid;
				    owner_array.push(obj);
				});
			}
			//if exist heritors
			if(ownerPathNatura != undefined) {
				//put in owner_array all users who are heritors
				jQuery.each(ownerPathNatura, function(key, value) {
					var obj = {};
					obj.owner_id = value.owner_id;
				    obj.contract_id = value.contract_id;
					obj.annex_id = annex_id;
				    obj.owner_area = value.area;
				    obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
					obj.renta_nat = ownersData[i].renta_nat_text;
				    obj.unpaid_renta_nat = value.unpaid_renta_nat;
				    obj.path = key;
				    obj.is_heritor = true;
				    obj.farming_id = farmingId;
					obj.uuid = value.uuid;
				    owner_array.push(obj);
				});
			}
		}

		object.payment_type = 'owner_payments';
	}

	object.rko_key = 'uuid'; // generate rko numbers by uuid
	object.nat_paid_amount = [];
	object.renta_nat_price_per_unit = [];
	object.renta_nat_total = [];
	object.renta_nat_type_id = [];

	object.owner_array = owner_array;
	object.year = jQuery('#search-year').combobox('getValue');
	object.payment_reduce = payment_reduce;
	object.payment_reduce = false;

	//data from add payment form
	var np_type_money = jQuery('#np-type-money > input').is(':checked');
	var np_type_natura = jQuery('#np-type-natura > input').is(':checked');
	var np_date = jQuery('#np-date > input').datebox('getValue');

	data.np_type_money = np_type_money;
	data.np_type_natura = np_type_natura;
	data.np_date = np_date;

	data.np_method_cash = jQuery('#np-method-cash > input').is(':checked');
	data.np_method_bank = jQuery('#np-method-bank > input').is(':checked');
	data.payment_order = jQuery('#np-order > input').is(':checked');
	data.weighing_note = jQuery('#np-weighing-note > input').is(':checked');
	data.np_recipient = jQuery('#np-recipient > input').val();
	data.np_recipient_egn = jQuery('#np-recipient-egn > input').val();
	data.np_recipient_proxy = jQuery('#np-recipient-proxy > input').val();
	data.np_recipient_address = jQuery('#np-recipient-address > input').val();
	data.np_recipient_lk = jQuery('#np-recipient-lk > input').val();
	data.np_bank_account = jQuery('#np-bank-account > input').val();
    data.payment_numbering = getPaymentNatNumbering();

    if (paymentsTable.length) {
        data.farming_id = jQuery('#contracts-tree').tree('getSelected').attributes['farming_id'];
    }
	if (np_type_money) {
		var renta_nat_prices = jQuery("input[id^='renta-nat-price-per-unit-']");
		for (var i = 0; i < renta_nat_prices.length; i++) {
			var renta_nat_price_per_unit = jQuery(renta_nat_prices[i]).numberspinner('getValue');
			var renta_nat_total = jQuery('#renta-nat-total-' + i).numberspinner('getValue');
			var renta_nat_type_id = ownersData[0].renta_nat_type_id[i];

			if(renta_nat_price_per_unit == '' || renta_nat_total == '') {
				jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.REQUIRED_FIELDS.message, 'error');
				return;
			}
			else if (renta_nat_price_per_unit == 0 || renta_nat_total == 0) {
				continue;
			}

			object.renta_nat_price_per_unit.push(renta_nat_price_per_unit);
			object.renta_nat_total.push(renta_nat_total);
			object.renta_nat_type_id.push(renta_nat_type_id);
		}

		if (object.renta_nat_price_per_unit.length > 0 && object.renta_nat_total.length > 0 && np_date) {

			TF.Rpc.Payments.AddPayment.saveNaturaPayment(object, data)
		    .done(function (dataObj) {
		    	completeSaveNatPayment(dataObj);

		    	jQuery('#win-add-nat-payment').window('close');

		    	if(paymentsTable.length > 0) {
		    		paymentsTable.treegrid('loadRpc');
		    	}
		    	else {
		    		ownerPaymentsTable.datagrid('loadRpc');
		    	}
		    })
		    .fail(function (errorObj) {
		    	jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
		    });
		} else {
			jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MISSING_DATA_TRANSACTION.message, 'error');
		}
	}
	else if (np_type_natura)
	{
		for (var i = 0; i < ownersData[0].renta_nat_type_id.length; i++) {
			var renta_nat_paid_amount = jQuery('#renta-nat-paid-amount-' + i).numberspinner('getValue');
			var renta_nat_type_id = ownersData[0].renta_nat_type_id[i];

			if (renta_nat_paid_amount == '') {
				jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.REQUIRED_FIELDS.message, 'error');
				return;
			}
			else if (renta_nat_paid_amount == 0) {
				continue;
			}

			object.nat_paid_amount.push(renta_nat_paid_amount);
			object.renta_nat_type_id.push(renta_nat_type_id);
		}

		if (np_date && object.nat_paid_amount.length > 0)
		{
			TF.Rpc.Payments.AddPayment.saveNaturaPayment(object, data)
		    .done(function (dataObj) {
		    	completeSaveNatPayment(dataObj);

		    	jQuery('#win-add-nat-payment').window('close');

		    	if(paymentsTable.length > 0) {
		    		paymentsTable.treegrid('loadRpc');
		    	}
		    	else {
		    		ownerPaymentsTable.datagrid('loadRpc');
		    	}

		    })
		    .fail(function (errorObj) {
		    	jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
		    });
		}
		else
		{
			jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MISSING_DATA_TRANSACTION.message, 'error');
		}
	}
}

function validatePersonalUsePayment() {
	var owner_array = [];
	var object = {};
	var data = {};
	var i = 0;
	var paymentsTable = jQuery('#contract-payments-tables');
	var ownersData = null;
	var year;
	var annex_id = null;

	if(jQuery('#personal-use-report-tables').length) {
		ownersData = [jQuery('#personal-use-report-tables').treegrid('getSelected')];
		year = jQuery('#pu-collections-search-year').combobox('getValue')
		data.farming_id = ownersData[0].farming_id;
	} else {
		ownersData = paymentsTable.treegrid('getSelections');
		year = jQuery('#search-year').combobox('getValue');
		let contractInfo = jQuery('#contracts-tree').tree('getSelected');
		data.farming_id = contractInfo.attributes['farming_id'];
		annex_id = contractInfo.attributes['annex_id'];
	}
	for (i = 0; i < ownersData.length; i++) {
		var obj = {};
		obj.owner_id = ownersData[i].owner_id;
		obj.charged_renta = ownersData[i].charged_renta;
		obj.contract_id = ownersData[i].contract_id;
		obj.annex_id = annex_id;
		obj.path = ownersData[i].path;
		obj.is_heritor = ownersData[i].is_heritor;
		obj.personal_use = ownersData[i].personal_use;
		obj.owner_area = ownersData[i].owner_area;
		obj.uuid = ownersData[i].uuid;
		owner_array.push(obj);
	}
	object.owner_array = owner_array;
	object.year = year;
	object.payment_reduce = false;
	object.nat_paid_amount = [];
	object.renta_nat_type_id = [];
	object.renta_nat_unit_value = [];
	// data from add payment form
	var np_type_money = jQuery('#np-type-money > input').is(':checked');
	var np_type_natura = jQuery('#np-type-natura > input').is(':checked');
	var np_date = jQuery('#np-date > input').datebox('getValue');
	data.np_type_money = np_type_money;
	data.np_type_natura = np_type_natura;
	data.np_date = np_date;
	data.payment_order = jQuery('#np-order > input').is(':checked');
	data.weighing_note = jQuery('#np-weighing-note > input').is(':checked');
	data.np_recipient = jQuery('#np-recipient > input').val();
	data.np_recipient_egn = jQuery('#np-recipient-egn > input').val();
	data.np_recipient_proxy = jQuery('#np-recipient-proxy > input').val();
	data.np_recipient_address = jQuery('#np-recipient-address > input').val();
	data.np_recipient_lk = jQuery('#np-recipient-lk > input').val();
	data.np_bank_account = jQuery('#np-bank-account > input').val();
	for (var i = 0; i < ownersData[0].personal_use_nat_type_id.length; i++) {
		if(jQuery('#renta-nat-paid-amount-' + i).length === 0) {
			continue;
		}
		var renta_nat_paid_amount = jQuery('#renta-nat-paid-amount-' + i).numberspinner('getValue');
		var renta_nat_type_id = ownersData[0].personal_use_nat_type_id[i];
		var renta_nat_unit_value = ownersData[0].personal_use_unit_value[i];
		if (renta_nat_paid_amount == '') {
			jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.REQUIRED_FIELDS.message, 'error');
			return;
		}
		else if (renta_nat_paid_amount == 0) {
			continue;
		}
		object.nat_paid_amount.push(renta_nat_paid_amount);
		object.renta_nat_type_id.push(renta_nat_type_id);
		object.renta_nat_unit_value.push(renta_nat_unit_value);
	}
	TF.Rpc.Payments.AddPayment.savePersonalUsePayment(object, data)
		.done(function (dataObj) {
			completeSaveNatPayment(dataObj);

			jQuery('#win-add-nat-payment').window('close');
			if(paymentsTable.length > 0) {
				paymentsTable.treegrid('loadRpc');
			}
			else {
				let selectedRowIndex = jQuery('#personal-use-report-tables').datagrid("getRowIndex", ownersData[0]);
				initPersonalUseReport('#collections-personal-use-toolbar', {pu_farming_year: jQuery('#pu-collections-search-year').combobox('getValue')}, selectedRowIndex);
			}
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
		});
}

function clearNaturaPayment() {
	jQuery('#np-bank-account-text').val('');
	jQuery('#np-recipient-text').val('');
	jQuery('#np-recipient-egn-text').val('');
	jQuery('#np-recipient-proxy-text').val('');
	jQuery('#np-date-text').val('');
	jQuery('#np-type-natura-radio').prop("checked", false);
	jQuery('#np-type-money-radio').prop("checked", true);
	jQuery('#natura-payment-order-checkbox').prop("checked", false);
	jQuery('#nat-rko-type-row').show();
	jQuery('#nat_rko_type_declaration_row').hide();
	
	jQuery('#js-natura-renta-payment-info tr:not(:first)').remove();
	jQuery('#js-renta-nat-leva-payment tr:not(:first)').remove();
	jQuery('#js-renta-nat-nat-payment tr:not(:first)').remove();
	jQuery('#js-renta-nat-nat-payment tr').remove();
}

function getPaymentInfoForNumbering() {
		var owner_array = new Array(),
			object = new Object(),
			data = new Object();

	var paymentsTable = jQuery('#contract-payments-tables');
	var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');

	if(paymentsTable.length){
		var ownersData = paymentsTable.treegrid('getSelections');

		for (var i = 0; i < ownersData.length; i++) {
			var obj ={};
			obj.owner_id = ownersData[i].owner_id;
			obj.charged_renta = ownersData[i].charged_renta;
			obj.contract_id = ownersData[i].contract_id;
			obj.path = ownersData[i].path;
			obj.is_heritor = ownersData[i].is_heritor;
			obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
			obj.renta_nat = ownersData[i].renta_nat_text;
			obj.unpaid_renta = ownersData[i].unpaid_renta;
			obj.unpaid_renta_nat = ownersData[i].unpaid_renta_nat_arr;
			obj.uuid = ownersData[i].uuid;
			owner_array.push(obj);
		}

		var renta_nat_types = ownersData[0].renta_nat_type.split('</br>');

	}
	else {
		var ownersData = ownerPaymentsTable.datagrid('getSelections');

		for (var i = 0; i < ownersData.length; i++) {

			var ownerIdNatura = ownersData[i].id_renta_natura;
			var ownerPathNatura = ownersData[i].path_renta_natura;
            var farmingId = ownersData[i].farming_id;
			//if exist owners
			if(ownerIdNatura != undefined) {

				//put in owner_array all users who are owners
				jQuery.each(ownerIdNatura, function(key, value) {
					var obj = {};
				    obj.owner_id = key;
				    obj.contract_id = value.contract_id;
				    obj.owner_area = value.area;
				    obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
					obj.renta_nat = ownersData[i].renta_nat_text;
					obj.unpaid_renta = value.unpaid_renta;
				    obj.unpaid_renta_nat = value.unpaid_renta_nat;
				    obj.is_heritor = false;
				    obj.farming_id = farmingId;
					obj.uuid = value.uuid;
				    owner_array.push(obj);
				});
			}

			//if exist heritors
			if(ownerPathNatura != undefined) {

				//put in owner_array all users who are heritors
				jQuery.each(ownerPathNatura, function(key, value) {
					var obj = {};
					obj.owner_id = value.owner_id;
				    obj.contract_id = value.contract_id;
				    obj.owner_area = value.area;
				    obj.charged_renta_nat = ownersData[i].charged_renta_nat_text;
					obj.renta_nat = ownersData[i].renta_nat_text;
				    obj.unpaid_renta_nat = value.unpaid_renta_nat;
				    obj.path = key;
				    obj.is_heritor = true;
				    obj.farming_id = farmingId;
					obj.uuid = value.uuid;
				    owner_array.push(obj);
				});
			}
		}

		object.payment_type = 'owner_payments';

		var renta_nat_types = ownersData[0].renta_nat_type.split('</br>');
	}

	object.rko_key = 'uuid'; // generate rko numbers by uuid
	object.nat_paid_amount =[];
	object.renta_nat_price_per_unit = [];
	object.renta_nat_total = [];

	object.owner_array = owner_array;
	object.year = jQuery('#search-year').combobox('getValue');
	object.renta_nat_type_id = ownersData[0].renta_nat_type_id;
	object.payment_reduce = payment_reduce;

	//data from add payment form
	var np_type_money = jQuery('#np-type-money > input').is(':checked');
	var np_type_natura = jQuery('#np-type-natura > input').is(':checked');
	var np_date = jQuery('#np-date > input').datebox('getValue');

	data.np_type_money = np_type_money;
	data.np_type_natura = np_type_natura;
	data.np_date = np_date;
    if (paymentsTable.length) {
        data.farming_id = jQuery('#contracts-tree').tree('getSelected').attributes['farming_id'];
    }

	data.np_method_cash = jQuery('#np-method-cash > input').is(':checked');
	data.np_method_bank = jQuery('#np-method-bank > input').is(':checked');
	data.payment_order = jQuery('#np-order > input').is(':checked');
	data.weighing_note = jQuery('#np-weighing-note > input').is(':checked');
	data.np_recipient = jQuery('#np-recipient > input').val();
	data.np_recipient_egn = jQuery('#np-recipient-egn > input').val();
	data.np_recipient_proxy = jQuery('#np-recipient-proxy > input').val();
	data.np_bank_account = jQuery('#np-bank-account > input').val();

	var renta_nat_prices = jQuery("input[id^='renta-nat-price-per-unit-']");

	if (np_type_money)
	{
		var total_flag = false;
		var price_per_init_flag = false;


		for (var i = 0; i < renta_nat_prices.length; i++) {
			if (jQuery(renta_nat_prices[i]).numberspinner('getValue') != '' && price_per_init_flag == false) {
				price_per_init_flag = true;
			}
			if (jQuery('#renta-nat-total-' + i).numberspinner('getValue') != '' && total_flag == false) {
				total_flag = true;
			}
			if(renta_nat_types[i] == '' || renta_nat_types[i] == 0){
				continue;
			}

			object.renta_nat_price_per_unit[i] = jQuery(renta_nat_prices[i]).numberspinner('getValue');
			object.renta_nat_total[i] = jQuery('#renta-nat-total-' + i).numberspinner('getValue');
		}
	}
	else if (np_type_natura)
	{
		var amount_input_flag = false;

		for (var i = 0; i < renta_nat_prices.length; i++) {
			if (jQuery(renta_nat_prices[i]).numberspinner('getValue') != '' && amount_input_flag == false) {
				amount_input_flag = true;
			}
			if(renta_nat_types[i] == '' || renta_nat_types[i] == 0) {
				continue;
			}

			object.nat_paid_amount[i] = jQuery('#renta-nat-paid-amount-' + i).numberspinner('getValue');
		}
	}

	return {data: data, object: object};
}


function clearRkoNatNumbering(index) {
    jQuery(`#rko-nat-number-${index}`).val('');
}

function clearRkoNatNumbers() {
    jQuery('#rko-nat-numbering').html('');
    jQuery('#win-add-nat-payment').window('resize', {
        height: jQuery('#btn-add-nat-payment').offset().top - jQuery('#addNatPaymentBeginningMarker').offset().top + 135
    });
}

function generateRkoNatNumbers() {

    if (jQuery('#np-type-money-radio').is(':checked') == true) {
        inputData = getPaymentInfoForNumbering();

        TF.Rpc.Payments.AddPayment.generateRkoNatNumbers(inputData.object, inputData.data)
        .done(function (data) {
            generateNatNumberingTemplate(data);
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
        });
    }
}

function generateNatNumberingTemplate(rows) {
    clearRkoNatNumbers();

    if (jQuery('#np-type-money-radio').is(':checked') == true) {
        jQuery('#generate-natura-payment-order').prop('checked', true);
    } else {
        jQuery('#generate-natura-payment-order').prop('checked', false);
    }

	var parent = jQuery('#rko-nat-numbering');
	Object.entries(rows).forEach(([rkoKey, rkoNumber], index) => {
		const rkoCounter = index + 1;
		var tr = jQuery('<tr></tr>');
        tr.addClass('rkoNatNumberingRow');
        var td1 = jQuery('<td></td>');
        td1.css({
            "text-align": "right",
            "width": "125px"
        });
        td1.html(`Плащане №${rkoCounter}`);
        tr.append(td1);

        var td2 = jQuery('<td></td>');
        td2.append(
			rkoNumber 
			? `<input type="text" id="rko-nat-number-${rkoCounter}" data-rko-key="${rkoKey}" value="${rkoNumber}">`
			: `<input type="text" id="rko-nat-number-${rkoCounter}" data-rko-key="${rkoKey}">`
		);

        tr.append(td2);

        var td3 = jQuery('<td></td>');
        td3.css({
            "padding-right": "10px"
        });

        var clearBtn = jQuery(`<a
				href="javaScript:void(0)"
				onClick="clearRkoNatNumbering(${rkoCounter})"
				title="Изчисти полето"
				class="easyui-linkbutton"
				data-options="iconCls:\'icon-cancel\'"
				style="width: 30px;" 
				id="clearRko-${rkoCounter}"
			>&nbsp;</a>
		`)
		.appendTo(td3);

        clearBtn.linkbutton();
        tr.append(td3);
        parent.append(tr);

	});

    jQuery('#win-add-nat-payment').window('resize', {
        height: jQuery('#btn-add-nat-payment').offset().top - jQuery('#addNatPaymentBeginningMarker').offset().top + 135
    });
       
}

function getPaymentNatNumbering() {
    var rows = jQuery('.rkoNatNumberingRow'),
        output = {};

    for (var i = 0; i < rows.length; i++) {
		const rkoKey = jQuery('#rko-nat-number-'+(i+1)).data('rko-key');
        const rkoNumber = jQuery('#rko-nat-number-'+(i+1)).val()
        
        output[rkoKey] = rkoNumber;
    };

    return output;
}

/**
 * [fillPayByNatOwnerInfo description]
 *
 * @param   {[type]}  data  [data description]
 *
 * @return  {[type]}        [return description]
 */
function fillPayByNatOwnerInfo(data) {
    jQuery('#np-recipient > input').val(data.owner_names);
    jQuery('#np-bank-account > input').val(data.iban);
    jQuery('#np-recipient-egn > input').val(data.egn_eik);
	jQuery('#np-bank-account > input').val(data.iban);
    jQuery('#np-recipient-address > input').val(data.address);
    jQuery('#np-recipient-lk > input').val(
        (data.lk_nomer && data.lk_nomer.length > 0 ? data.lk_nomer : '') +
        (data.lk_nomer && data.lk_izdavane && data.lk_izdavane.length > 0 ? ', ' + data.lk_izdavane : '')
    );
}

/**
 * [fillPayByNatRepInfo description]
 *
 * @param   {[type]}  data  [data description]
 *
 * @return  {[type]}        [return description]
 */
function fillPayByNatRepInfo(data) {
    jQuery('#np-recipient > input').val(data.rep_names);
    jQuery('#np-recipient-egn > input').val(data.rep_egn);
    jQuery('#np-bank-account > input').val(data.rep_iban);
    jQuery('#np-recipient-address > input').val(data.rep_address);
    jQuery('#np-recipient-lk > input').val(
        (data.rep_lk && data.rep_lk.length > 0 ? data.rep_lk : '') +
        (data.rep_lk && data.rep_lk_izdavane && data.rep_lk_izdavane.length > 0 ? ', ' + data.rep_lk_izdavane : '')
    );
}
