<?php

/**
 * Integration test script for PaymentsRepository CTE implementation.
 * 
 * This script tests the CTE implementation against a real database
 * and compares results with the original implementation where possible.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Repositories\PaymentsRepository;
use Illuminate\Support\Facades\DB;

class CteIntegrationTest
{
    private PaymentsRepository $repository;
    private array $testResults = [];
    
    public function __construct()
    {
        $this->repository = new PaymentsRepository();
    }
    
    /**
     * Run all integration tests.
     */
    public function runAllTests(): void
    {
        echo "Starting CTE Integration Tests...\n\n";
        
        $this->testDatabaseConnection();
        $this->testGetOwnerContracts();
        $this->testGetPaymentPlots();
        $this->testParameterValidation();
        $this->testPerformance();
        
        $this->printSummary();
    }
    
    /**
     * Test database connection.
     */
    private function testDatabaseConnection(): void
    {
        echo "Testing database connection...\n";
        
        try {
            $result = DB::select('SELECT version() as version');
            $this->recordSuccess('Database Connection', 'Connected to: ' . $result[0]->version);
        } catch (Exception $e) {
            $this->recordFailure('Database Connection', $e->getMessage());
        }
    }
    
    /**
     * Test getOwnerContracts CTE implementation.
     */
    private function testGetOwnerContracts(): void
    {
        echo "Testing getOwnerContracts CTE...\n";
        
        try {
            // Test basic functionality
            $result = $this->repository->getOwnerContracts(2023, []);
            $count = $result->count();
            $this->recordSuccess('getOwnerContracts Basic', "Returned {$count} records");
            
            // Test with filters
            $filterParams = [
                'owner_id' => 1,
                'payroll_farming' => [1, 2]
            ];
            $filteredResult = $this->repository->getOwnerContracts(2023, $filterParams);
            $filteredCount = $filteredResult->count();
            $this->recordSuccess('getOwnerContracts Filtered', "Returned {$filteredCount} records with filters");
            
            // Validate structure if we have results
            if ($result->isNotEmpty()) {
                $firstItem = $result->first();
                $requiredFields = ['contract_id', 'parent_id', 'owner_ids'];
                
                foreach ($requiredFields as $field) {
                    if (!property_exists($firstItem, $field)) {
                        throw new Exception("Missing required field: {$field}");
                    }
                }
                $this->recordSuccess('getOwnerContracts Structure', 'All required fields present');
            }
            
        } catch (Exception $e) {
            $this->recordFailure('getOwnerContracts', $e->getMessage());
        }
    }
    
    /**
     * Test getPaymentPlots CTE implementation.
     */
    private function testGetPaymentPlots(): void
    {
        echo "Testing getPaymentPlots CTE...\n";
        
        try {
            // Test basic functionality
            $result = $this->repository->getPaymentPlots(2023);
            $count = $result->count();
            $this->recordSuccess('getPaymentPlots Basic', "Returned {$count} records");
            
            // Test with various parameters
            $result2 = $this->repository->getPaymentPlots(2023, 1, null, null, []);
            $count2 = $result2->count();
            $this->recordSuccess('getPaymentPlots with Contract ID', "Returned {$count2} records");
            
            // Test with owner filter
            $result3 = $this->repository->getPaymentPlots(2023, null, 1, null, []);
            $count3 = $result3->count();
            $this->recordSuccess('getPaymentPlots with Owner ID', "Returned {$count3} records");
            
            // Test with complex filters
            $filterParams = [
                'payroll_farming' => [1, 2],
                'payroll_ekate' => ['12345', '67890']
            ];
            $result4 = $this->repository->getPaymentPlots(2023, null, null, null, $filterParams);
            $count4 = $result4->count();
            $this->recordSuccess('getPaymentPlots with Complex Filters', "Returned {$count4} records");
            
            // Validate structure if we have results
            if ($result->isNotEmpty()) {
                $firstItem = $result->first();
                $requiredFields = ['contract_id', 'owner_id', 'plot_id', 'owner_names'];
                
                foreach ($requiredFields as $field) {
                    if (!property_exists($firstItem, $field)) {
                        throw new Exception("Missing required field: {$field}");
                    }
                }
                $this->recordSuccess('getPaymentPlots Structure', 'All required fields present');
            }
            
        } catch (Exception $e) {
            $this->recordFailure('getPaymentPlots', $e->getMessage());
        }
    }
    
    /**
     * Test parameter validation.
     */
    private function testParameterValidation(): void
    {
        echo "Testing parameter validation...\n";
        
        try {
            // Test invalid year - should throw exception
            try {
                $this->repository->getOwnerContracts(1800, []);
                $this->recordFailure('Parameter Validation', 'Invalid year should throw exception');
            } catch (InvalidArgumentException $e) {
                $this->recordSuccess('Parameter Validation', 'Invalid year correctly rejected');
            }
            
            // Test valid year range
            $result = $this->repository->getOwnerContracts(2023, []);
            $this->recordSuccess('Parameter Validation', 'Valid year accepted');
            
        } catch (Exception $e) {
            $this->recordFailure('Parameter Validation', $e->getMessage());
        }
    }
    
    /**
     * Test query performance.
     */
    private function testPerformance(): void
    {
        echo "Testing query performance...\n";
        
        try {
            // Test getOwnerContracts performance
            $startTime = microtime(true);
            $this->repository->getOwnerContracts(2023, []);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($executionTime < 5000) { // Less than 5 seconds
                $this->recordSuccess('getOwnerContracts Performance', "{$executionTime}ms execution time");
            } else {
                $this->recordFailure('getOwnerContracts Performance', "Slow execution: {$executionTime}ms");
            }
            
            // Test getPaymentPlots performance
            $startTime = microtime(true);
            $this->repository->getPaymentPlots(2023);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($executionTime < 10000) { // Less than 10 seconds
                $this->recordSuccess('getPaymentPlots Performance', "{$executionTime}ms execution time");
            } else {
                $this->recordFailure('getPaymentPlots Performance', "Slow execution: {$executionTime}ms");
            }
            
        } catch (Exception $e) {
            $this->recordFailure('Performance Test', $e->getMessage());
        }
    }
    
    /**
     * Record a successful test.
     */
    private function recordSuccess(string $testName, string $message): void
    {
        $this->testResults[] = [
            'test' => $testName,
            'status' => 'PASS',
            'message' => $message
        ];
        echo "  ✓ {$testName}: {$message}\n";
    }
    
    /**
     * Record a failed test.
     */
    private function recordFailure(string $testName, string $message): void
    {
        $this->testResults[] = [
            'test' => $testName,
            'status' => 'FAIL',
            'message' => $message
        ];
        echo "  ✗ {$testName}: {$message}\n";
    }
    
    /**
     * Print test summary.
     */
    private function printSummary(): void
    {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "TEST SUMMARY\n";
        echo str_repeat("=", 50) . "\n";
        
        $passed = 0;
        $failed = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['status'] === 'PASS') {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        echo "Total Tests: " . count($this->testResults) . "\n";
        echo "Passed: {$passed}\n";
        echo "Failed: {$failed}\n";
        
        if ($failed > 0) {
            echo "\nFAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        echo "\n" . ($failed === 0 ? "ALL TESTS PASSED! ✓" : "SOME TESTS FAILED! ✗") . "\n";
    }
}

// Run the tests if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $tester = new CteIntegrationTest();
    $tester->runAllTests();
}
