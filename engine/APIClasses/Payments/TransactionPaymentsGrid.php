<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Transaction Payments Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id transactions-payments-grid
 */
class TransactionPaymentsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readTransactionPaymentsGrid'],
                'validators' => [
                    'transactionId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read transaction payments grid.
     *
     * @api-method read
     *
     * @param string $transactionId -The Transaction id
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows             -The results
     *               #item string total           -The count of all results
     *               }
     */
    public function readTransactionPaymentsGrid(int $transactionId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
        ];

        if (!isset($transactionId) || !(int)$transactionId) {
            return $default;
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
					WHERE ir.id IN 
						(SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo 
							WHERE ipo.owner_id = o.id
							AND ipo.pc_rel_id IN
								(SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable} 
								WHERE contract_id IN (c.id)))";

        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'tablename' => $UserDbController->DbHandler->tablePayments,
            'return' => [
                'p.id', 'max(t.id) as transaction_id',
                'c.c_num', 'c.c_date', 'max(p.date) as date',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner",
                "({$repQuery}) as rep_names",
                'max(round(p.amount::numeric, 2)) as amount',
                'array_agg(pn.nat_type order by pn.nat_type asc) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 3) order by pn.nat_type asc) as amount_nat',
                'max(t.paid_from) as paid_from',
                'max(t.paid_in) as paid_in',
                'c.farming_id',
            ],
            'sort' => $sort,
            'order' => $order,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'where' => [
                'transaction' => ['column' => 'transaction_id', 'compare' => '=', 'prefix' => 'p', 'value' => $transactionId],
            ],
            'group' => 'p.id, c.id, o.id',
        ];

        $counter = $UserDbPaymentsController->getPaymentsByParams($options, true, false, 'all');
        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbPaymentsController->getPaymentsByParams($options, false, false, 'all');
        $resultsCount = count($results);
        // total
        $options = [
            'return' => [
                'max(t.amount) as amount',
                'array_agg(pn.nat_type) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 3)) as amount_nat',
                'max(t.paid_from) as paid_from',
                'max(t.paid_in) as paid_in',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 't', 'compare' => '=', 'value' => $transactionId],
            ],
            'sort' => $sort,
            'order' => $order,
            'group' => 't.id',
        ];

        $allResults = $UserDbPaymentsController->getTransactionsByParams($options, false, false);
        $allResultsCount = count($allResults);
        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);

        $farmingOptions = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($farmingOptions, false, false);
        $farmingCount = count($farming_results);
        $finalFarmings = [];

        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
            $finalFarmings[$farming_results[$i]['id']]['eik'] = $farming_results[$i]['bulstat'];
        }

        // form renta types array
        $renta_types = [];
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        // iterate and convert results to grid format
        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['ttype'] = 'от ' . (1 == $results[$i]['paid_from'] ? 'Лева' : 'Натура') . ' в ' . (1 == $results[$i]['paid_in'] ? 'Лева' : 'Натура');
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
            $results[$i]['farming_id'] = $finalFarmings[$results[$i]['farming_id']]['name'];
            if (1 == $results[$i]['paid_from']) {
                $results[$i]['paid_from_text'] = $results[$i]['amount'] . ' лв';
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $results[$i]['paid_from_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            if (1 == $results[$i]['paid_in']) {
                $results[$i]['paid_in_text'] = $results[$i]['amount'] . ' лв';
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $results[$i]['paid_in_text'] .= $amount_nat[$j] . ' X [' . $renta_types[$payment_nat_types[$j]] . ']<br/>';
                }
            }

            $results[$i]['c_num'] = $results[$i]['c_num'] . ' / ' . strftime('%d.%m.%Y', strtotime($results[$i]['c_date']));

            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $results[$i]['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $results[$i]['rep_names'] = implode(', <br/>', $tmp_reps_array);
            }
        }

        // get sum of colums ttype, paid_in_text, paid_from_text

        $paid_in_natura_text_arr = [];
        $paid_in_leva_text = 0;
        $paid_from_natura_text_arr = [];
        $paid_from_leva_text = 0;
        for ($i = 0; $i < $allResultsCount; $i++) {
            // paid_in_text
            if (1 == $allResults[$i]['paid_in']) {
                $paid_in_leva_text += $allResults[$i]['amount'];
            } else {
                $payment_nat_types = explode(',', trim($allResults[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);

                $amount_nat = explode(',', trim($allResults[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $tmp_payments_nat_types[$payment_nat_types[$j]] += $amount_nat[$j];
                }

                if (count($tmp_payments_nat_types) > 0) {
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $paid_in_natura_text_arr[$renta_type] += $value;
                    }
                }
            }

            if (1 == $allResults[$i]['paid_from']) {
                $paid_from_leva_text += $allResults[$i]['amount'];
            } else {
                $payment_nat_types = explode(',', trim($allResults[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);

                $amount_nat = explode(',', trim($allResults[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $tmp_payments_nat_types[$payment_nat_types[$j]] += $amount_nat[$j];
                }

                if (count($tmp_payments_nat_types) > 0) {
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $paid_from_natura_text_arr[$renta_type] += $value;
                    }
                }
            }
        }

        // formatted total paid_in_text
        $paid_in_arr = [];

        if ($paid_in_leva_text > 0) {
            $paid_in_arr[] = number_format($paid_in_leva_text, 2, '.', '') . ' лв';
        }
        if (count($paid_in_natura_text_arr) > 0) {
            ksort($paid_in_natura_text_arr);

            foreach ($paid_in_natura_text_arr as $renta_type => $value) {
                if ($renta_type && $value) {
                    $paid_in_arr[] = number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']';
                }
            }
        }
        $paid_in_text = implode('</br>', $paid_in_arr);

        // formatted total paid_from_text
        $paid_from_arr = [];

        if ($paid_from_leva_text > 0) {
            $paid_from_arr[] = number_format($paid_from_leva_text, 2, '.', '') . ' лв';
        }
        if (count($paid_from_natura_text_arr) > 0) {
            ksort($paid_from_natura_text_arr);

            foreach ($paid_from_natura_text_arr as $renta_type => $value) {
                if ($renta_type && $value) {
                    $paid_from_arr[] = number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']';
                }
            }
        }
        $paid_from_text = implode('</br>', $paid_from_arr);

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'date' => '<b>ОБЩО</b>',
                    'paid_in_text' => $paid_in_text,
                    'paid_from_text' => $paid_from_text,
                ],
            ],
        ];
    }
}
