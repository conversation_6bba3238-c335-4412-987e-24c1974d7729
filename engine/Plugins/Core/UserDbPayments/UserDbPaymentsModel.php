<?php

namespace TF\Engine\Plugins\Core\UserDbPayments;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbPaymentsModel extends UserDbModel
{
    public function getPaymentsForOwners($options, $counter, $returnOnlySQL)
    {
        // chosen year is used multiple times in query
        $chosen_year = $options['chosen_year'];

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true AND a.start_date <= :start_date AND a.due_date >= :due_date)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON(o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.owner_id = o.id AND pu.year in ({$chosen_year}) AND pu.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableChargedRenta} cr ON(cr.contract_id = c.id AND cr.plot_id = kvs.gid AND cr.year = :charged_year AND cr.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} rep ON(rep.id = po.rep_id)";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);

            return str_replace(':charged_year', "'" . $chosen_year . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':charged_year', $chosen_year);

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaymentsForOwnersWithLastAnnex($options, $counter, $returnOnlySQL)
    {
        // chosen year is used multiple times in query
        $chosen_year = $options['chosen_year'];

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN (SELECT * from (SELECT parent_id AS ID, is_annex,start_date,due_date,c_num,renta_nat_type_id,renta,renta_nat,
                    row_number() over (PARTITION by a.parent_id ORDER BY due_date DESC) as rn FROM su_contracts A
                        WHERE A .is_annex = TRUE AND A .start_date <= :start_date AND A .due_date >= :due_date AND A .active = TRUE AND parent_id = {$options['contract_id_string']} ) t
                            where rn = 1 ) A  ON A . ID = C . ID";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON(o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableChargedRenta} cr ON(cr.contract_id = c.id AND cr.plot_id = kvs.gid AND cr.year = :charged_year AND cr.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} rep ON(rep.id = po.rep_id)";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);

            return str_replace(':charged_year', "'" . $chosen_year . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':charged_year', $chosen_year);

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getChargedRentaParams($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(crp.id))';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$this->chargedRentaParams} crp";
        $sql .= " LEFT JOIN {$this->chargedRentaNaturaParams} crnp on (crp.id = crnp.params_id)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaymentsForContracts($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(c.id))';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= ' LEFT JOIN (SELECT DISTINCT ON(parent_id) * '
            . "FROM {$this->tableContracts} "
            . 'WHERE is_annex = true '
            . 'AND active = true ';

        if ($options['start_date']) {
            $sql .= 'AND start_date <= :start_date ';
        }

        if ($options['due_date']) {
            $sql .= 'AND due_date >= :due_date ';
        }

        $sql .= 'ORDER BY parent_id, due_date DESC) a '
            . 'ON a.parent_id = c.id';
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} c_r ON ((CASE WHEN A.id IS NULL THEN C.id ELSE A.id END) = c_r.contract_id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= ' LEFT JOIN su_plots_rents spr ON (spr.pc_rel_id = pc.id)';
        $sql .= ' LEFT JOIN su_contracts_rents_types scrt ON (scrt.id = spr.rent_type_id)';
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON (o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)";
        $sql .= " LEFT JOIN {$this->tableChargedRenta} cr ON cr.contract_id = c.id AND cr.plot_id = kvs.gid and cr.owner_id = o.id " . ($options['charged_year'] ? ' AND cr.year = :charged_year' : '');
        $sql .= " LEFT JOIN {$this->tableNaturaChargedRenta} crn ON crn.renta_id = cr.id";
        $sql .= " LEFT JOIN {$this->tableContractsGroup} cg ON cg.id = c.group";

        $sql .= ' WHERE true';
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND po.owner_id IN({$options['owner_id_string']})";
        }
        if ($options['without_contract_natura']) {
            $sql .= ' AND c_r.renta_id is NULL';
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if (isset($options['contract_group'])) {
            $sql .= ' AND(
                CASE
                    WHEN a.id IS NOT NULL THEN a.group
                    ELSE c.group
                END
            ) = :contract_group';
        }

        if ($options['specific_rents']) {
            $sql .= $options['specific_rents'];
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['charged_year']) {
            $cmd->bindParameter(':charged_year', $options['charged_year']);
        }
        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }
        if ($options['contract_group']) {
            $cmd->bindParameter(':contract_group', $options['contract_group']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaymentsByParams($options, $counter, $returnOnlySQL, $status = 'true')
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tablePayments} p";
        $sql .= " LEFT JOIN {$this->tableNaturaPayments} pn ON(p.id = pn.payment_id)";
        $sql .= " INNER JOIN {$this->tableTransactions} t ON(t.id = p.transaction_id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON(o.id = p.owner_id)";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = p.contract_id)";

        $pc_rel = 'c.id';
        if (!isset($options['annexes_no_join'])) {
            $sql .= " left join lateral (
                            select * from {$this->tableContracts} an where an.parent_id = c.id
                            order by an.id desc
                            limit 1
                       )  a on true";
            $pc_rel = '(CASE WHEN a.id IS NULL THEN c.id ELSE a.id END)';
        }

        if (!empty($options['join_ekattes'])) {
            $sql .= " LEFT JOIN LATERAL (
                        SELECT string_agg(distinct ekate.ekatte_name || '(' || ekate.ekatte_code || ')', ', ') as ekattes
                        FROM {$this->contractsPlotsRelTable} pc
                        LEFT JOIN {$this->tableKVS} kvs ON (kvs.gid = pc.plot_id)
                        LEFT JOIN {$this->tableEkatte} ekate ON (ekate.ekatte_code = kvs.ekate)
                        WHERE pc.contract_id = (CASE WHEN a.id IS null THEN c.id ELSE a.id END)
            ) ekate ON TRUE";
        }

        if (!empty($options['join_owners'])) {
            $sql .= " LEFT JOIN LATERAL (
                        SELECT *
                        FROM {$this->plotsOwnersRelTable} po
                        LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = (case when a.id is null then c.id else a.id end))
                        WHERE po.pc_rel_id = pc.id and po.owner_id = p.owner_id
                        GROUP BY ekate.ekate, ekate.ekatte_name
            ) po ON TRUE";
        }

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ('all' != $status) {
            $sql .= " AND t.status = {$status} ";
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND p.contract_id IN({$options['contract_id_string']})";
        }
        if ($options['owner_id_string']) {
            $sql .= " AND p.owner_id IN({$options['owner_id_string']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getCollectionsByParams($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableCollections} coll";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = coll.contract_id)";

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND p.contract_id IN({$options['contract_id_string']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaymentsByParamsWithKvs($options, $counter, $returnOnlySQL, $status = 'true')
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tablePayments} p";
        $sql .= " INNER JOIN {$this->tableTransactions} t ON(t.id = p.transaction_id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON(o.id = p.owner_id)";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = p.contract_id)";
        $pc_rel = 'c.id';
        if (!isset($options['annexes_no_join'])) {
            $sql .= " LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id)";
            $pc_rel = '(CASE WHEN a.id IS NULL THEN c.id ELSE a.id END)';
        }

        if (!isset($options['rent_nature_no_join'])) {
            $sql .= " LEFT JOIN {$this->tableNaturaPayments} pn ON(p.id = pn.payment_id)";
        }

        if (!empty($options['join_kvs'])) {
            $sql .= " LEFT JOIN LATERAL (
                        SELECT ekate
                        FROM {$this->tableKVS} kvs
                        LEFT JOIN {$this->contractsPlotsRelTable} pc ON pc.contract_id = {$pc_rel}
                        WHERE kvs.gid = pc.plot_id
                        GROUP BY ekate
                    ) kvs on TRUE ";
        }

        if ($options['joins'] && is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ('all' != $status) {
            $sql .= " AND t.status = {$status} ";
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['caseWhere']) {
            $sql .= $options['caseWhere'];
        }

        if ($options['contract_id_string']) {
            $sql .= " AND p.contract_id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND p.owner_id IN({$options['owner_id_string']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= " GROUP BY {$options['group']}";
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= " ORDER BY {$options['sort']} {$options['order']}";
        }

        if ($options['union_dividends']) {
            $dividendsReturn = $this->createReturnVariable($options['return_dividends'], $counter, $options['custom_counter']);

            $dividendsSql = "SELECT {$dividendsReturn} FROM {$this->tableDividendsPayment} dp";
            $dividendsSql .= " INNER JOIN {$this->tableCooperators} c ON(c.id = dp.cooperator_id)";

            $dividendsSql .= ' WHERE true';

            if ($options['where_dividends']) {
                $dividendsSql = $this->createWhereSQL($dividendsSql, $options['where_dividends'], $returnOnlySQL);
            }

            if (isset($options['group_dividends']) && false === $counter) {
                $dividendsSql .= " GROUP BY {$options['group_dividends']}";
            }

            if ($options['having_dividends'] && false === $counter) {
                $dividendsSql .= " HAVING {$options['having_dividends']}";
            }

            if ($options['order'] && $options['sort_dividends'] && false == $counter) {
                $dividendsSql .= " ORDER BY {$options['sort_dividends']} {$options['order']}";
            }

            $sql = "({$sql}) union all ({$dividendsSql})";

            if (isset($options['union_group']) && false === $counter) {
                $sql .= " GROUP BY {$options['union_group']}";
            }

            if ($options['order'] && $options['union_sort'] && false == $counter) {
                $sql .= " ORDER BY {$options['union_sort']} {$options['order']}";
            }
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['union_dividends']) {
            $this->createWhereBinds($cmd, $options['where_dividends']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * @param int $id
     * @param string $user_name
     *
     * @throws TDbException
     *
     * @return int
     */
    public function disableTransactionsById($id, $user_name)
    {
        $sql = "UPDATE {$this->tableTransactions} SET status = false, cancelled_by = :user_name WHERE id = :id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->bindParameter(':user_name', $user_name);

        return $cmd->execute();
    }

    public function getTransactionsByParams($options, $counter, $returnOnlySQL, $status = 'true')
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableTransactions} t";
        // $sql .= " LEFT JOIN {$this->tableNaturaTransactions} tn ON(tn.transaction_id = t.id)";
        $sql .= " JOIN {$this->tablePayments} p ON(p.transaction_id = t.id)";
        $sql .= " JOIN {$this->tableOwners} o ON(o.id = p.owner_id)";
        $sql .= " LEFT JOIN {$this->tableNaturaPayments} pn ON(p.id = pn.payment_id)";
        $sql .= " LEFT JOIN {$this->tableContracts} c ON(c.id = p.contract_id)";

        $sql .= ' WHERE true';

        if ('all' != $status) {
            $sql .= " AND t.status = {$status} ";
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['transaction_types']) {
            $sql .= ' AND (';
            $transaction_types_count = count($options['transaction_types']);
            for ($i = 0; $i < $transaction_types_count; $i++) {
                $value = $options['transaction_types'][$i];
                $payment_in_id = $GLOBALS['Payments']['types'][$value]['payment_in_id'];
                $payment_from_id = $GLOBALS['Payments']['types'][$value]['payment_from_id'];

                $sql .= "(t.paid_in = {$payment_in_id} AND t.paid_from = {$payment_from_id})";

                if ($i != ($transaction_types_count - 1)) {
                    $sql .= ' OR';
                }
            }
            $sql .= ')';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersHeritorsPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableOwners} o";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(o.id = po.owner_id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.id = po.pc_rel_id)";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.owner_id = o.id AND pu.contract_id = c.id AND year = :year)";
        $sql .= " LEFT JOIN {$this->tableHeritors} h on (o.id = ltree2text(subltree(h.path, 0, 1))::numeric)";
        $sql .= " LEFT JOIN {$this->tableOwners} oh on (h.owner_id = oh.id) WHERE true";
        $sql .= ' AND (c.start_date <= :start_date AND c.due_date >= :due_date)';
        $sql .= " AND (o.id = :owner_id OR h.path ~ ('*.'||:owner_id||'.*')::lquery)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':year', $options['year']);
        $cmd->bindParameter(':owner_id', $options['owner_id']);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableOwners} o";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(o.id = po.owner_id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.id = po.pc_rel_id)";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.owner_id = o.id AND pu.pc_rel_id = pc.id " . (!$options['addedOnly'] ? ' AND year = :year' : '') . ')';
        $sql .= " LEFT JOIN {$this->tablePersonalUseRents} pur ON pur.pu_id = pu.id";

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }
        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['year']) {
            $cmd->bindParameter(':year', $options['year']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tablePersonalUse} pu";
        $sql .= " INNER JOIN {$this->tableContracts} c ON(c.id = pu.contract_id)";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $year = '';
        if ($options['year']) {
            $year .= 'AND pu.year = ' . $options['year'];
        }

        $sql = "SELECT {$return} FROM {$this->contractsPlotsRelTable} cprel";
        $sql .= ' JOIN layer_kvs kvs ON(kvs.gid = cprel.plot_id)';
        $sql .= " INNER JOIN {$this->tablePersonalUse} pu ON(pu.pc_rel_id = cprel.id {$year})";
        $sql .= ' LEFT JOIN su_personal_use_rents pur ON(pur.pu_id = pu.id)';

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function deletePersonalUse($ids)
    {
        $sql = 'DELETE FROM su_personal_use WHERE id IN (' . implode(',', $ids) . ')';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getOwnerPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $owner_id = '';
        if ($options['owner_id'] && false === $counter) {
            $owner_id .= 'AND pu.owner_id = ' . $options['owner_id'];
        }

        $year = '';
        if ($options['year'] && false === $counter) {
            $year .= 'AND pu.year = ' . $options['year'];
        }

        $sql = "SELECT {$return} FROM {$this->contractsPlotsRelTable} pc";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pc.id = pu.pc_rel_id {$owner_id} {$year})";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->read();
    }

    public function getCollectionsPersonalUse($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tablePersonalUse} pu ";
        $sql .= ' LEFT JOIN su_personal_use_rents pur ON(pur.pu_id = pu.id)';
        $sql .= ' LEFT JOIN su_contracts_plots_rel cpr ON cpr.id = pu.pc_rel_id';
        $sql .= ' LEFT JOIN su_contracts c ON c.id = cpr.contract_id';
        $sql .= ' LEFT JOIN su_contracts p ON p.id = c.parent_id';
        $sql .= ' LEFT JOIN su_owners o ON o.id = pu.owner_id';
        $sql .= ' LEFT JOIN su_renta_types srt ON srt.id = pur.renta_type';

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }
        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $this->createWhereBinds($cmd, $orWhere);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getContractOwnerDistributionData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT 
                    {$return} 
                FROM su_plots_owners_rel spor
                left join su_contracts_plots_rel scpr on scpr.id = spor.pc_rel_id and scpr.annex_action = 'added'
                left join layer_kvs kvs on kvs.gid = scpr.plot_id
                left join su_contract_owner_rel cor on cor.owner_id = spor.owner_id and cor.contract_id = scpr.contract_id
                left join su_personal_use pu on pu.pc_rel_id = scpr.id and pu.owner_id = spor.owner_id and pu.year = " . $options['year'] . ' 
                left join su_personal_use_rents pur on pur.pu_id = pu.id 
        ';
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPayrollData($options, $counter, $returnOnlySQL)
    {
        $year_id = implode(',', $options['year_id']);
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->tableContracts} c"

            . ' LEFT JOIN (SELECT * from (SELECT parent_id, id, is_annex,start_date,due_date,c_num,renta_nat_type_id,renta,renta_nat,
                    row_number() over (PARTITION by a.parent_id ORDER BY due_date DESC) as rn FROM su_contracts A
                        WHERE A .is_annex = TRUE AND A .start_date <= :start_date AND A .due_date >= :due_date AND A .active = TRUE) t
                            where rn = 1 ) A  ON A.parent_id = C . ID'

            . " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))"
            . " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)"
            . " LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)"
            . " LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)"
            . " LEFT JOIN {$this->tableOwnersReps} r ON(r.id = po.rep_id)"
            . " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.owner_id = o.id AND pu.year in ({$year_id}) AND pu.pc_rel_id = pc.id)"
            . " LEFT JOIN {$this->tableChargedRenta} cr ON(cr.plot_id = kvs.gid AND cr.contract_id = c.id AND cr.year in ({$year_id}) AND cr.owner_id = po.owner_id)";

        if (is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        if (!$options['without_table_charged_natura']) {
            $sql .= " LEFT JOIN LATERAL (SELECT SUM(crn.amount * crn.nat_unit_price) as converted_charged_renta_nat FROM {$this->tableNaturaChargedRenta} crn WHERE cr.id = crn.renta_id AND crn.nat_is_converted IS TRUE) crn ON TRUE";
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['rent_place']) {
            $sql .= " AND (o.rent_place=:rent_place1 OR (kvs.ekate=:rent_place2 AND o.rent_place=''))";
        }

        if ($options['rep_rent_place']) {
            $sql .= " AND (r.rent_place=:rep_rent_place1 OR (kvs.ekate=:rep_rent_place2 AND r.rent_place=''))";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }

        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['year_id']) {
            $cmd->bindParameter(':year_id', $options['year_id']);
        }

        if ($options['rent_place']) {
            $cmd->bindParameter(':rent_place1', $options['rent_place']);
            $cmd->bindParameter(':rent_place2', $options['rent_place']);
        }

        if ($options['rep_rent_place']) {
            $cmd->bindParameter(':rep_rent_place1', $options['rep_rent_place']);
            $cmd->bindParameter(':rep_rent_place2', $options['rep_rent_place']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPayrollDataWithPayments($options, $counter, $returnOnlySQL)
    {
        $year_id = implode(',', $options['year_id']);
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->tableContracts} c";

        $sql .= ' LEFT JOIN (SELECT * from (SELECT parent_id, id, is_annex, start_date, due_date, c_num, renta_nat_type_id, renta, renta_nat,
                  row_number() over (PARTITION by a.parent_id ORDER BY due_date DESC) as rn FROM su_contracts A
                  WHERE A.is_annex = TRUE AND A.start_date <= :start_date AND A.due_date >= :due_date AND A.active = TRUE) t
                  where rn = 1) A  ON A.parent_id = c.id';

        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu on (pu.owner_id = o.id and pu.year in ({$year_id}) and pu.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableChargedRenta} scr ON ((
                     (scr.owner_id = po.owner_id or scr.owner_id :: TEXT :: ltree @> po. PATH)
                     AND scr.plot_id = pc.plot_id
                     AND scr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                     and scr.year in ({$year_id})
                 )";
        $sql .= " LEFT JOIN LATERAL (
                select
                (CASE WHEN A.ID IS NULL THEN C.ID ELSE A.ID END) || ';' || array_to_string(array_agg(crt.renta_id), ',') as ids,
                (CASE WHEN A.ID IS NULL THEN C.ID ELSE A.ID END) || ';' || array_to_string(array_agg(crt.renta_value), ',') as qt,
                (CASE WHEN A.ID IS NULL THEN C.ID ELSE A.ID END) || ';' || array_to_string(array_agg(crt.renta_id), '-') || ':'||
                array_to_string(array_agg(round(cast(crt.renta_value * ((po.percent * pc.area_for_rent / 100) - COALESCE(pu.area, 0)) as DECIMAL), 3)), '|') as due_rent_nat
                from su_contracts_rents crt where crt.contract_id = (CASE WHEN A.ID IS NULL THEN C.ID ELSE A.ID END) and renta_id is not null
            ) as nat on true";

        $sql .= " left join lateral (
                    select string_agg( distinct cast(json_build_array(nat_type , charged_renta_nat , converted_charged_renta_nat,nat_is_converted) as text), '|'
                    ) AS item
                    from charged_rentas_mat_view
                  where true and start_date <= :start_date AND due_date >= :due_date
                  and year in ({$year_id}) and owner_id = o.id and c_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END) group by owner_id ) as charged_nat on true ";

        $sql .= " left join lateral (
                    select string_agg( distinct cast(json_build_array(nat_type , charged_renta_nat , converted_charged_renta_nat, nat_is_converted) as text), '|'
                    ) AS a_item
                    from charged_rentas_annexes_mat_view
                  where true and start_date <= :start_date AND due_date >= :due_date
                  and year in ({$year_id}) and owner_id = o.id and c_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END) group by owner_id ) as charged_a_nat on true ";
        $sql .= " LEFT JOIN (
            select
            string_agg(distinct(p.contract_id || ';' || p.id || ':' || p.amount), ',') as amount,
            string_agg(distinct(p.contract_id || ';' || p.id || ':' || p.paid_from), ',') as paid_from,
            string_agg(distinct(p.contract_id || ';' || p.id || ':' || p.paid_in), ',') as paid_in,
            string_agg(distinct(p.contract_id || ';' || p.id || ':' || pn.nat_type || '|' || pn.amount), ',' ) as nat,
            contract_id,
            owner_id
            from su_payments p
            left join su_transactions t on (t.id = p.transaction_id)
            left join su_payments_natura pn on (pn.payment_id = p.id)
            where true and t.status = true AND p.farming_year IN ({$year_id})
            group by p.owner_id, contract_id
            ) as P1 on (P1.owner_id = o.id and P1.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }
        if (!$counter && $options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }

        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['year_id']) {
            $cmd->bindParameter(':year_id', $options['year_id']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaidData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = "SELECT {$return} FROM {$this->tablePayments} p";
        $sql .= " LEFT JOIN {$this->tableNaturaPayments} pn ON(p.id = pn.payment_id)";
        $sql .= " LEFT JOIN {$this->tableRentaTypes} rent ON(pn.nat_type = rent.id)";
        $sql .= " JOIN {$this->tableTransactions} t ON(t.id = p.transaction_id)";
        $sql .= ' WHERE t.status ';

        if (empty($options['where']['type'])) {
            $sql .= ' and t.type = ' . TRANSACTION_TYPE_PAYMENT;
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPaymentsForOwnersReport($options, $counter, $returnOnlySQL)
    {
        // chosen year is used multiple times in query
        $chosen_year = implode(',', $options['chosen_year']);
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true AND a.start_date <= :start_date AND a.due_date >= :due_date)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON(o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableChargedRenta} cr ON(cr.contract_id = c.id AND cr.plot_id = kvs.gid AND cr.year IN ({$chosen_year}))";// :charged_year)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} rep ON(rep.id = po.rep_id)";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);

            return str_replace(':charged_year', "'" . $chosen_year . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':charged_year', $chosen_year);

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPersonalUseForOwners($options, $counter, $returnOnlySQL)
    {
        // chosen year is used multiple times in query
        $chosen_year = $options['chosen_years'];
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $contractJoinDatesConditions = '';

        if ($options['start_date']) {
            $contractJoinDatesConditions .= ' AND a.start_date <= :start_date ';
        }

        if ($options['due_date']) {
            $contractJoinDatesConditions .= ' AND a.due_date >= :due_date ';
        }

        $sql .= " LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true " . $contractJoinDatesConditions . ')';
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.year in ({$chosen_year}) AND pu.pc_rel_id = pc.id)";
        $sql .= ' LEFT JOIN su_personal_use_rents spur on spur.pu_id = pu.id';
        $sql .= ' LEFT JOIN su_renta_types srt on srt.id = spur.renta_type';
        $sql .= ' LEFT JOIN su_contracts_plots_rel pc_pu ON pc_pu.id = pu.pc_rel_id ';
        $sql .= ' LEFT JOIN su_contracts c_pu ON c_pu.id = pc_pu.contract_id';
        $sql .= ' WHERE spur.area > 0 and c_pu.id = c.id';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND c.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);

            return str_replace(':charged_year', "'" . $chosen_year . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }

        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getRentaNaturaByContractsAndNaturaType($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = cp.contract_id
                INNER JOIN su_contracts c ON c.id = p.contract_id
                INNER JOIN su_payments_natura pn on pn.payment_id = p.id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getRentaNaturaContracts($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = cp.contract_id
                INNER JOIN su_contracts C ON C . ID = P .contract_id
                INNER JOIN su_payments_natura pn on pn.payment_id = p.id WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getPaidRentaNaturaAmount($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);
        $sql = "SELECT {$return} FROM {$options['tablename']} as p
                INNER JOIN su_payments_natura pn on pn.payment_id = p.id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getContractsWithRentaNaturaData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        if ($options['farming_year']) {
            $yearSql = '(';
            foreach ($options['farming_year'] as $year) {
                $yearSql .= '(start_date <= \'' . $GLOBALS['Farming']['years'][$year]['year'] . '-09-30\' AND ';
                $yearSql .= 'due_date >= \'' . ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01\')';
                $yearSql .= ' OR ';
            }
            $yearSql = substr($yearSql, 0, -4);
            $yearSql .= ')';
        }

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                INNER JOIN layer_kvs kvs on cp.plot_id = kvs.gid
                INNER JOIN su_contracts_rents cr on cr.contract_id = cp.contract_id
                inner join su_contracts c on cp.contract_id = c.id
                WHERE true";

        if ($options['farming_year']) {
            $sql .= ' AND ' . $yearSql;
        }

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getRentasAndAreasByContractsAndNaturaType($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        if ($options['farming_year']) {
            $yearSql = '(';
            foreach ($options['farming_year'] as $ayear) {
                $yearSql .= '(start_date <= \'' . $GLOBALS['Farming']['years'][$ayear]['year'] . '-09-30\' AND ';
                $yearSql .= 'due_date >= \'' . ($GLOBALS['Farming']['years'][$ayear]['year'] - 1) . '-10-01\')';
                $yearSql .= ' OR ';
            }
            $yearSql = substr($yearSql, 0, -4);
            $yearSql .= ')';
        }

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                INNER JOIN su_contracts_rents cr on cr.contract_id = cp.contract_id
                INNER JOIN su_contracts c on cp.contract_id = c.id
                WHERE true";
        if ($options['farming_year']) {
            $sql .= ' AND ' . $yearSql;
        }

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getNaturaAmountDueByContracts($options, $counter, $returnOnlySQL)
    {
        $aReturn = $this->getRentasAndAreasByContractsAndNaturaType($options, $counter, $returnOnlySQL);

        $iNaturaAmountDue = '';
        for ($i = 0; $i < count($aReturn); $i++) {
            $iNaturaAmountDue = $iNaturaAmountDue + ($aReturn[$i]['renta_value'] * $aReturn[$i]['area']);
        }

        return number_format((float)$iNaturaAmountDue, 2, '.', '');
    }

    public function getNaturaCalculatedAmountByContracts($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $forOwner = '';
        if ($options['join_by_owner_id']) {
            $forOwner = $options['join_by_owner_id'];
        }

        $sql = "SELECT {$return} FROM {$options['tablename']} as crn
                INNER JOIN su_charged_renta cr on cr.id = crn.renta_id
                INNER JOIN su_contracts_plots_rel cp on cp.contract_id = cr.contract_id AND cp.plot_id = cr.plot_id
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id {$forOwner}
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getStavkaNaturaCalculatedAmountByContracts($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as crn
                INNER JOIN su_charged_renta cr on cr.id = crn.renta_id
                INNER JOIN su_contracts_plots_rel cp on cp.contract_id = cr.contract_id
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function calculateUnpaidAndOverpaidRentaNat($renta_nat, $charged_renta_nat, $paid_renta_nat)
    {
        $aCalculatedValue = [];
        $unpaid_renta_nat = 0;
        $overpaid_renta_nat = 0;

        if (null == $charged_renta_nat) {
            if ($paid_renta_nat > $renta_nat) {
                $overpaid_renta_nat = $paid_renta_nat - $renta_nat;
            } elseif ($paid_renta_nat < $renta_nat) {
                $unpaid_renta_nat = $renta_nat - $paid_renta_nat;
            }
        }

        if (null != $charged_renta_nat) {
            if ($paid_renta_nat > $charged_renta_nat) {
                $overpaid_renta_nat = $paid_renta_nat - $charged_renta_nat;
            }
            if ($paid_renta_nat < $charged_renta_nat) {
                $unpaid_renta_nat = $charged_renta_nat - $paid_renta_nat;
            }
        }

        return [
            'unpaid_renta_nat' => $unpaid_renta_nat,
            'overpaid_renta_nat' => $overpaid_renta_nat,
        ];
    }

    public function editOrAddChargeRentaNatura($options)
    {
        $tableChargedRenta = $this->tableChargedRenta;
        $tableNaturaChargedRenta = $this->tableNaturaChargedRenta;
        $where = $this->createWhereSQL($sql, $options['where'], false);
        $natType = $options['mainData']['nat_type'];

        $sqlSel = "SELECT id FROM {$tableChargedRenta} WHERE True ";
        $sqlSel .= $where;
        $cmd = $this->DbModule->createCommand($sqlSel);
        $this->createWhereBinds($cmd, $options['where']);
        $rentaId = $cmd->queryScalar();

        $sqlSel = "SELECT * FROM {$tableNaturaChargedRenta} WHERE renta_id={$rentaId} AND nat_type={$natType}";
        $cmd = $this->DbModule->createCommand($sqlSel);
        $res = $cmd->query()->readAll();

        if (count($res)) {
            $this->editItem([
                'tablename' => $tableNaturaChargedRenta,
                'mainData' => $options['mainData'],
                'where' => ['renta_id' => $res[0]['renta_id'], 'nat_type' => $natType],
            ]);
        } else {
            $options['mainData']['renta_id'] = $rentaId;
            $options['tablename'] = $tableNaturaChargedRenta;

            $this->addItem($options);
        }

        return $natType;
    }

    public function hasContractRentNat($contractId, $annexId, $natType, $plotRentId = null)
    {
        if (null == $plotRentId) {
            // Check if the contract have this renta type.
            $isRentaInContract = $this->getItemsByParams([
                'tablename' => $this->contractsRentsRelTable,
                'return' => ['*'],
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
                    'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'value' => $natType],
                ],
            ], true, false);

            return 0 < $isRentaInContract[0]['count'];
        }

        // Check if the contract has this renta type in specific rents
        $rentaInContractTypes = $this->getItemsByParams([
            'tablename' => $this->contractsRentsTypesTable,
            'return' => ['*'],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => ($annexId ?? $contractId)],
                'rents' => ['column' => 'rents', 'compare' => '@>', 'value' => '{"rent_nature": [{"id": "' . $natType . '"}]}'],
            ],
        ], false, false);

        if (count($rentaInContractTypes) > 0) {
            $plotRent = $this->getItemsByParams([
                'tablename' => 'su_plots_rents',
                'return' => ['*'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $plotRentId],
                ],
            ], false, false);

            foreach ($rentaInContractTypes as $rentaInContractType) {
                if ($rentaInContractType['id'] == $plotRent[0]['rent_type_id']) {
                    return true;
                }
            }
        }

        return false;
    }

    public function getPaymentReports($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = cp.contract_id
                INNER JOIN su_contracts c ON c.id = p.contract_id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getPaidAmount($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as p
                INNER JOIN su_contracts as c on c.id = p.contract_id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getRentasAndAreas($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as c
                INNER JOIN su_contracts_plots_rel cp on cp.contract_id = c.id
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = c.id
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                LEFT JOIN su_contracts a on (a.parent_id = c.id)
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getAmountDue($options, $counter, $returnOnlySQL)
    {
        $aReturn = $this->getRentasAndAreas($options, $counter, $returnOnlySQL);

        $iAmountDue = '';
        for ($i = 0; $i < count($aReturn); $i++) {
            $iAmountDue = $iAmountDue + ($aReturn[$i]['renta'] * $aReturn[$i]['area']);
        }

        return number_format((float)$iAmountDue, 2, '.', '');
    }

    public function getRentasAndAreasNatura($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as c
        INNER JOIN su_contracts_plots_rel cp on cp.contract_id = c.id
        INNER JOIN su_contracts_rents cr on cr.contract_id = cp.contract_id
        INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
        INNER JOIN su_payments p on p.contract_id = c.id
        INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
        LEFT JOIN su_contracts a on (a.parent_id = c.id)
        WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    // Площ в съответното землище
    public function getAreaUsed($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as c
                INNER JOIN su_contracts_plots_rel cp on cp.contract_id = c.id
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = c.id
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                LEFT JOIN su_contracts a on (a.parent_id = c.id)
                WHERE true";

        $aReturn = $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);

        $used_area = [];

        if (isset($options['grouped_by_contract']) && true == $options['grouped_by_contract']) {
            for ($i = 0; $i < count($aReturn); $i++) {
                $used_area[$aReturn[$i]['id']] = $used_area[$aReturn[$i]['id']] + $aReturn[$i]['area'];
            }

            return $used_area;
        }

        $iAreaUsed = 0;
        for ($i = 0; $i < count($aReturn); $i++) {
            $AreaUsed = $AreaUsed + $aReturn[$i]['area'];
        }

        return number_format((float)$AreaUsed, 3, '.', '');
    }

    public function getChargedRentasAndAreas($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cr
                INNER JOIN su_contracts_plots_rel cp on cp.contract_id = cr.contract_id
                INNER JOIN su_payments p on p.contract_id = cr.contract_id
                INNER JOIN su_contracts as c on c.id = cr.contract_id
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getChargedAmount($options, $counter, $returnOnlySQL)
    {
        $aReturn = $this->getChargedRentasAndAreas($options, $counter, $returnOnlySQL);

        $iAmount = '';
        for ($i = 0; $i < count($aReturn); $i++) {
            $iAmount = $iAmount + ($aReturn[$i]['renta'] * $aReturn[$i]['area']);
        }

        return number_format((float)$iAmount, 2, '.', '');
    }

    public function calculateUnpaidAndOverpaidRenta($renta, $charged_renta, $paid_renta)
    {
        $aCalculatedValue = [];
        $unpaid_renta = 0;
        $overpaid_renta = 0;

        if (null == $charged_renta) {
            if ($paid_renta > $renta) {
                $overpaid_renta = $paid_renta - $renta;
            } elseif ($paid_renta < $renta) {
                $unpaid_renta = $renta - $paid_renta;
            }
        }

        if (null != $charged_renta) {
            if ($paid_renta > $charged_renta) {
                $overpaid_renta = $paid_renta - $charged_renta;
            }
            if ($paid_renta < $charged_renta) {
                $unpaid_renta = $charged_renta - $paid_renta;
            }
        }

        return [
            'unpaid_renta' => $unpaid_renta,
            'overpaid_renta' => $overpaid_renta,
        ];
    }

    public function getDetailedPaymentReportsByDate($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cp
                INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
                INNER JOIN su_payments p on p.contract_id = cp.contract_id
                INNER JOIN su_contracts c ON c.id = p.contract_id
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getContractsByOwnerPaymentsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c ";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc on (c.id = pc.contract_id) ";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po on (pc.id = po.pc_rel_id) ";
        $sql .= " INNER JOIN {$this->tableOwners} o on (po.owner_id = o.id) ";
        $sql .= " LEFT JOIN {$this->tableHeritors} h on (o.id = ltree2text(subltree(h.path, 0, 1))::numeric) ";
        $sql .= " LEFT JOIN {$this->tableOwners} oh on (h.owner_id = oh.id) WHERE true ";
        $sql .= ' AND (c.start_date <= :start_date AND c.due_date >= :due_date) ';
        $sql .= " AND (o.id = :owner_id OR h.path ~ ('*.'||:owner_id||'.*')::lquery)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND id NOT IN (' . $options['anti_id_string'] . ')';
        }

        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }
        if ($options['owner_id']) {
            $cmd->bindParameter(':owner_id', $options['owner_id']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractsOrAnnexByOwnerPaymentsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c ";
        $sql .= 'LEFT JOIN (SELECT * FROM (SELECT id as annex_id, parent_id, due_date,
                    ROW_NUMBER () OVER (PARTITION BY A .parent_id ORDER BY due_date DESC) AS rn
                        FROM su_contracts A WHERE A .is_annex = TRUE
                            AND A .start_date <= :start_date AND A .due_date >= :due_date AND A .active = TRUE
                                ) T WHERE rn = 1) a on c.id = a.parent_id';
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc on (c.id = pc.contract_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po on (pc.id = po.pc_rel_id)";
        $sql .= " INNER JOIN {$this->tableOwners} o on (po.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableHeritors} h on (o.id = ltree2text(subltree(h.path, 0, 1))::numeric)";
        $sql .= " LEFT JOIN {$this->tableOwners} oh on (h.owner_id = oh.id) WHERE true";
        $sql .= ' AND (c.start_date <= :start_date AND c.due_date >= :due_date)';
        $sql .= " AND (o.id = :owner_id OR h.path ~ ('*.'||:owner_id||'.*')::lquery)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND id NOT IN (' . $options['anti_id_string'] . ')';
        }

        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }
        if ($options['owner_id']) {
            $cmd->bindParameter(':owner_id', $options['owner_id']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getAnnexesByOwnerPaymentsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM ";
        $sql .= " (SELECT DISTINCT(c.parent_id), c.id, max(c.due_date) as due_date
                    FROM {$this->tableContracts} C";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc on (c.id = pc.contract_id)";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po on (pc.id = po.pc_rel_id)";
        $sql .= " INNER JOIN {$this->tableOwners} o on (po.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableHeritors} h on (o.id = ltree2text(subltree(h.path, 0, 1))::numeric)";
        $sql .= " LEFT JOIN {$this->tableOwners} oh on (h.owner_id = oh.id) WHERE true";
        $sql .= ' AND (c.start_date <= :start_date AND c.due_date >= :due_date)';
        $sql .= " AND (o.id = :owner_id OR h.path ~ ('*.'||:owner_id||'.*')::lquery)";
        $sql .= " AND C .nm_usage_rights IN (2, 3, 5)
                    AND C .active = 'TRUE'
                        AND C .is_annex = 'TRUE'
                            GROUP BY c.parent_id, c.id) data
                                ORDER BY parent_id, due_date DESC";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND id NOT IN (' . $options['anti_id_string'] . ')';
        }

        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }
        if ($options['owner_id']) {
            $cmd->bindParameter(':owner_id', $options['owner_id']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractRentaNatura($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = " SELECT {$return} FROM {$this->tableContracts} as c";
        $sql .= " INNER JOIN {$this->contractsRentsRelTable} cr on (c.id = cr.contract_id)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getChargedRentasNatura($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} as cr
            INNER JOIN su_contracts_plots_rel cp on cp.contract_id = cr.contract_id
            INNER JOIN su_payments p on p.contract_id = cr.contract_id
            INNER JOIN su_contracts as c on c.id = cr.contract_id
            INNER JOIN layer_kvs kvs on kvs.gid = cp.plot_id
            INNER JOIN su_plots_owners_rel po on po.pc_rel_id = cp.id
            INNER JOIN su_charged_renta_natura crn on crn.renta_id = cr.id";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }
        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getLastGeneratedRkoNumber($options, $returnOnlySQL)
    {
        $sql = 'SELECT DISTINCT ON (farming_id) farming_id,
                rko_number
            FROM
                su_payments
            WHERE
                rko_number IS NOT NULL
            AND farming_id NOTNULL
            ORDER BY
                farming_id,
                rko_number COLLATE "alpha_numeric_bg" DESC';
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);
        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function setRkoNumberingStart($newNum, $farmingId)
    {
        $sql = 'UPDATE
            su_payments
        SET
            rko_number = :newNum
        WHERE
            farming_id = :farmingId
        AND
            id = (
                    CASE WHEN
                        (SELECT MIN(id) FROM su_payments WHERE id > (SELECT MAX(id) FROM su_payments WHERE rko_number IS NOT NULL AND farming_id = :farmingId) AND rko_number is NULL AND farming_id = :farmingId) is NULL
                    THEN
                        (SELECT MAX(id) FROM su_payments WHERE farming_id = :farmingId)
                    ELSE
                        (SELECT MIN(id) FROM su_payments WHERE id > (SELECT MAX(id) FROM su_payments WHERE rko_number IS NOT NULL AND farming_id = :farmingId) AND rko_number is NULL AND farming_id = :farmingId)
                    END)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':newNum', $newNum);
        $cmd->bindParameter(':farmingId', $farmingId);

        return $cmd->execute();
    }

    public function getPaymentDocNumbersByTransactionID($transaction_id)
    {
        $sql = 'SELECT id, rko_number FROM su_payments WHERE transaction_id = :transaction_id ORDER BY id desc';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':transaction_id', $transaction_id);

        return $cmd->query()->readAll();
    }

    public function updateRkoNumberByPaymentId($paymentArray)
    {
        $sql = 'UPDATE
            su_payments
        SET
            rko_number = :newNum
        WHERE
            id = :id';

        $tmpVar = '' != $paymentArray['rko_number'] ? $paymentArray['rko_number'] : null;
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':newNum', $tmpVar, PDO::PARAM_STR);
        $cmd->bindParameter(':id', $paymentArray['id']);

        return $cmd->execute();
    }

    public function getCurrentAnnexId($contractID, $startDate, $dueDate)
    {
        $sql = 'SELECT
                    a.id
                FROM
                    su_contracts C
                LEFT JOIN su_contracts A ON (
                    A .parent_id = C . ID
                    AND A .active = TRUE
                    AND A .start_date <= :due_date
                    AND A .due_date >= :start_date )
                WHERE c.id = :contract_id
        ';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $startDate);
        $cmd->bindParameter(':due_date', $dueDate);
        $cmd->bindParameter(':contract_id', $contractID);

        return $cmd->query()->readAll();
    }

    public function getPaymentsByBankAndNatura($options, $counter, $returnOnlySQL)
    {
        $sql = 'select
						distinct on (p.id, pn.id) p.id, c.c_num as c_num,
						t.recipient as owner_name,
                        o.egn,
						(case when p.paid_in = 1 THEN (case when rt.id is null then rt.id else null end) else rt.id END) as rent_id,
						(case when p.paid_in = 1 THEN (case when rt.name is null then rt.name else null end) else rt.name END) as rent_name,
						(case when p.paid_in = 1 THEN (case when rt.unit is null then rt.unit else null end) else rt.unit END) as rent_unit,
						(case when p.paid_in = 2 then pn.amount else (case when p.paid_from = 2 then (pn.amount * pn.unit_value) else p.amount end) END) as amount,
						lk.ekate,
                        o.id as owner_id,
                        cpr.id as pc_rel_id,
                        p.path as path,
                        c.id as contract_id,
                        CASE
                            WHEN EXISTS (
                                SELECT 1
                                FROM su_plots_owners_rel spor_check
                                INNER JOIN su_contracts_plots_rel cpr_check ON cpr_check.id = spor_check.pc_rel_id
                                WHERE spor_check.owner_id = o.id
                                AND cpr_check.contract_id = c.id
                                AND ((spor_check.path is null and p.path is null) or spor_check.path = p.path)
                            ) THEN true
                            ELSE false
                        END as participate_in_contract
					from
						su_payments p
						inner join su_transactions t ON(p.transaction_id = t.id)
						left join su_payments_natura pn on(p.id = pn.payment_id)
						left join  su_renta_types rt ON(rt.id = pn.nat_type)
						inner join su_owners o on(o.id = p.owner_id)
						inner join su_contracts c on(c.id = p.contract_id)
						left join su_contracts a on(a.parent_id = c.id)
                        left join su_contracts_plots_rel cpra ON(cpra.contract_id = a.id)
						left join layer_kvs lka ON(lka.gid = cpra.plot_id)
						left join su_contracts_plots_rel cpr on(cpr.contract_id = c.id)
						left join layer_kvs lk on (lk.gid = cpr.plot_id)
					where
						t.status = true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['dateNow']) {
            $sql .= ' AND (:dateNow BETWEEN (case when a.id is not null then a.c_date else c.c_date end) AND (case when a.id is not null then a.due_date else c.due_date end))';
        }

        if ($options['ecate']) {
            $sql .= ' AND ((case when a.id is not null then lka.ekate else lk.ekate end) = :ecate)';
        }

        if ($options['ekattes']) {
            $sql .= ' AND ((case when a.id is not null then lka.ekate else lk.ekate end) IN (:ekattes))';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "select {$return}
                    from (
                        select SUM(t1.amount) amount, t1.owner_name, t1.rent_id, t1.rent_name,t1.rent_unit, array_agg(distinct t1.c_num) as c_num, t1.egn,
                        -- participate_in_contract: true if owner participates in any plot of the contract, false if payment made but owner doesn't participate
                        bool_or(t1.participate_in_contract) as participate_in_contract
                        FROM(
                          {$sql}
                        ) as t1
                        group by t1.owner_name, t1.rent_id, t1.rent_name, t1.rent_unit,t1.egn
                    ) as t2
        ";

        if (!empty($options['c_num'])) {
            $sql .= ' where :c_num = any(t2.c_num::text[])';
        }

        $sql .= ' group by t2.owner_name, t2.c_num';

        if (!$counter && 'owner_name' == $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['dateNow']) {
            $cmd->bindParameter(':dateNow', $options['dateNow']);
        }

        if ($options['ecate']) {
            $cmd->bindParameter(':ecate', $options['ecate']);
        }

        if ($options['ekattes']) {
            $cmd->bindParameter(':ekattes', implode(',', $options['ekattes']));
        }

        if ($options['c_num']) {
            $cmd->bindParameter(':c_num', $options['c_num']);
        }

        return $cmd->query()->readAll();
    }

    public function getUnpaidRentaByOwner($options, $counter = false)
    {
        $columnsSql = '
            ';
        $groupBy = '';

        $sql = 'SELECT
                owner_names,
                egn_eik,
                sum(unpaid_leva) unpaid_leva,
                natura_agg(unpaid_natura) unpaid_natura
            FROM
                tf_payroll2 ( :start_date, :due_date, :farming_years::int4[], :farm_ids, :ekatte, false, :owner_names, :owner_egn, :owner_eik)';
        if ($options['sortByNat']) {
            $sql .= "
            LEFT JOIN LATERAL (
                SELECT
                    (elem ->> 'value') :: NUMERIC AS value
                FROM
                    json_array_elements (unpaid_natura) unpaid_natura (elem)
                WHERE
                    elem ->> 'id' = '{$options['sortByNat']}'
            ) natura ON TRUE
            ";
        }
        $sql .= 'WHERE
                NOT is_dead
            GROUP BY owner_names, egn_eik
            HAVING
            (
                natura_agg (unpaid_natura) NOTNULL
                AND tf_total_nat_val (natura_agg(unpaid_natura)) > 0
            )
            OR SUM (unpaid_leva) > 0
            ';
        if (!$counter) {
            if ($options['sort'] && !$options['order']) {
                $sql .= " ORDER BY {$options['sort']} ";
            }

            if ($options['sort'] && $options['order']) {
                $sql .= " ORDER BY {$options['sort']} {$options['order']} ";
            }
            if ($options['sortByNat']) {
                $sql .= ' ORDER BY SUM (natura. VALUE) DESC NULLS LAST';
            }

            if (isset($options['limit'], $options['offset']) && !$counter) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }
        if ($counter) {
            $sql = "SELECT
                count (*),
                sum( unpaid_leva) unpaid_leva,
                natura_agg(unpaid_natura) unpaid_natura
            FROM
            (
                {$sql}
            ) T";
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':start_date', $options['where']['start_date']['value']);
        $cmd->bindParameter(':due_date', $options['where']['due_date']['value']);
        $cmd->bindValue(':farming_years', '{' . implode(',', $options['where']['farming_years']['value']) . '}');
        $cmd->bindValue(':farm_ids', '{' . implode(',', $options['where']['farm_ids']['value']) . '}');
        $cmd->bindParameter(':ekatte', $options['where']['ekatte']['value']);
        $cmd->bindParameter(':owner_names', $options['where']['owner_names']['value']);
        $cmd->bindParameter(':owner_egn', $options['where']['owner_egn']['value']);
        $cmd->bindParameter(':owner_eik', $options['where']['owner_eik']['value']);

        if ($counter) {
            return $cmd->queryRow();
        }

        return $cmd->query()->readAll();
    }
}
