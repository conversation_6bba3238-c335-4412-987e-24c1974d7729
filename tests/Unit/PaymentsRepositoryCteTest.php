<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use Illuminate\Support\Facades\DB;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Repositories\PaymentsRepository;

/**
 * Test suite for PaymentsRepository CTE implementations.
 * 
 * Validates that the CTE-based queries produce the same results as the original raw SQL queries.
 */
class PaymentsRepositoryCteTest extends TestCase
{
    private PaymentsRepository $repository;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new PaymentsRepository();
    }

    /**
     * Test getOwnerContracts CTE implementation.
     * 
     * Validates that the CTE query returns expected structure and data.
     */
    public function testGetOwnerContractsCte()
    {
        // Test with a valid year
        $year = 2023;
        $filterParams = [];
        
        try {
            $result = $this->repository->getOwnerContracts($year, $filterParams);
            
            // Validate result structure
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            
            // If we have results, validate the structure
            if ($result->isNotEmpty()) {
                $firstItem = $result->first();
                
                // Check required fields are present
                $this->assertObjectHasAttribute('contract_id', $firstItem);
                $this->assertObjectHasAttribute('parent_id', $firstItem);
                $this->assertObjectHasAttribute('owner_ids', $firstItem);
            }
            
            $this->assertTrue(true, 'getOwnerContracts CTE executed successfully');
            
        } catch (\Exception $e) {
            $this->fail('getOwnerContracts CTE failed: ' . $e->getMessage());
        }
    }

    /**
     * Test getOwnerContracts with filters.
     */
    public function testGetOwnerContractsWithFilters()
    {
        $year = 2023;
        $filterParams = [
            'owner_id' => 1,
            'payroll_farming' => [1, 2]
        ];
        
        try {
            $result = $this->repository->getOwnerContracts($year, $filterParams);
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            $this->assertTrue(true, 'getOwnerContracts with filters executed successfully');
            
        } catch (\Exception $e) {
            $this->fail('getOwnerContracts with filters failed: ' . $e->getMessage());
        }
    }

    /**
     * Test getPaymentPlots CTE implementation.
     * 
     * Validates that the complex CTE query executes without errors.
     */
    public function testGetPaymentPlotsCte()
    {
        $year = 2023;
        
        try {
            $result = $this->repository->getPaymentPlots($year);
            
            // Validate result structure
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            
            // If we have results, validate the structure
            if ($result->isNotEmpty()) {
                $firstItem = $result->first();
                
                // Check some key fields are present
                $this->assertObjectHasAttribute('contract_id', $firstItem);
                $this->assertObjectHasAttribute('owner_id', $firstItem);
                $this->assertObjectHasAttribute('plot_id', $firstItem);
                $this->assertObjectHasAttribute('owner_names', $firstItem);
            }
            
            $this->assertTrue(true, 'getPaymentPlots CTE executed successfully');
            
        } catch (\Exception $e) {
            $this->fail('getPaymentPlots CTE failed: ' . $e->getMessage());
        }
    }

    /**
     * Test getPaymentPlots with various filters.
     */
    public function testGetPaymentPlotsWithFilters()
    {
        $year = 2023;
        $contractAnnexId = 1;
        $ownerId = 1;
        $path = null;
        $filterParams = [
            'payroll_farming' => [1, 2],
            'payroll_ekate' => ['12345']
        ];
        
        try {
            $result = $this->repository->getPaymentPlots($year, $contractAnnexId, $ownerId, $path, $filterParams);
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            $this->assertTrue(true, 'getPaymentPlots with filters executed successfully');
            
        } catch (\Exception $e) {
            $this->fail('getPaymentPlots with filters failed: ' . $e->getMessage());
        }
    }

    /**
     * Test parameter validation.
     */
    public function testParameterValidation()
    {
        // Test invalid year
        $this->expectException(\InvalidArgumentException::class);
        $this->repository->getOwnerContracts(1800, []);
    }

    /**
     * Test that CTE helper methods return query builders.
     */
    public function testCteHelperMethods()
    {
        $reflection = new \ReflectionClass($this->repository);
        
        // Test buildContractsWithAnnexesCte
        $method = $reflection->getMethod('buildContractsWithAnnexesCte');
        $method->setAccessible(true);
        
        $farmYearStart = '2022-10-01';
        $farmYearEnd = '2023-09-30';
        
        try {
            $result = $method->invoke($this->repository, $farmYearStart, $farmYearEnd);
            $this->assertInstanceOf(\Illuminate\Database\Query\Builder::class, $result);
            $this->assertTrue(true, 'buildContractsWithAnnexesCte returns query builder');
            
        } catch (\Exception $e) {
            $this->fail('buildContractsWithAnnexesCte failed: ' . $e->getMessage());
        }
    }

    /**
     * Test database connection and basic query execution.
     */
    public function testDatabaseConnection()
    {
        try {
            // Test basic DB connection
            $result = DB::select('SELECT 1 as test');
            $this->assertNotEmpty($result);
            $this->assertEquals(1, $result[0]->test);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection not available: ' . $e->getMessage());
        }
    }

    /**
     * Performance test - ensure CTE queries don't take too long.
     */
    public function testQueryPerformance()
    {
        $year = 2023;
        
        $startTime = microtime(true);
        
        try {
            $this->repository->getOwnerContracts($year, []);
            $endTime = microtime(true);
            
            $executionTime = $endTime - $startTime;
            
            // Query should complete within 30 seconds (adjust as needed)
            $this->assertLessThan(30, $executionTime, 'getOwnerContracts query took too long');
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Performance test skipped due to error: ' . $e->getMessage());
        }
    }

    /**
     * Test edge cases and error handling.
     */
    public function testEdgeCases()
    {
        // Test with empty filter params
        try {
            $result = $this->repository->getOwnerContracts(2023, []);
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            
        } catch (\Exception $e) {
            $this->fail('Empty filter params test failed: ' . $e->getMessage());
        }

        // Test with null values in filter params
        try {
            $filterParams = [
                'payroll_farming' => [1, null, 2]
            ];
            $result = $this->repository->getPaymentPlots(2023, null, null, null, $filterParams);
            $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
            
        } catch (\Exception $e) {
            $this->fail('Null values in filter params test failed: ' . $e->getMessage());
        }
    }
}
