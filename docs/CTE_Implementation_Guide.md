# PaymentsRepository CTE Implementation Guide

## Overview

This document describes the implementation of Common Table Expressions (CTEs) in the PaymentsRepository class using the `staudenmeir/laravel-cte` package. The implementation converts complex raw SQL queries into maintainable, modular CTE-based queries.

## Architecture

### CTE Components

The implementation breaks down complex queries into logical CTE components:

1. **contracts_with_annexes** - Handles contract-annex relationships
2. **plot_owner_data** - Manages plot, owner, and representative relationships
3. **rent_calculations** - Calculates rent values and areas
4. **owner_status** - Determines owner status and payment eligibility
5. **renta_nat_aggregation** - JSON aggregation for natural rent data
6. **charged_renta_nat_aggregation** - JSON aggregation for charged rent data

### Key Methods

#### getOwnerContracts()
- **Status**: ✅ Complete
- **Description**: Uses CTE to get owner contracts with proper filtering
- **Features**: 
  - Parameter validation
  - NOT EXISTS filtering for parent contracts
  - Support for multiple filter parameters

#### getPaymentPlots()
- **Status**: ✅ Complete (Basic version)
- **Description**: Complex query using multiple CTEs
- **Features**:
  - Multiple CTE composition
  - Window functions for area calculations
  - Complex filtering and joins
  - Parameter validation

#### getPaymentPlotsWithJsonAggregations()
- **Status**: 🔄 In Progress
- **Description**: Enhanced version with JSON aggregations
- **Features**:
  - JSON aggregation CTEs
  - LATERAL joins for complex data structures
  - UNION ALL for combining different data sources

## CTE Helper Methods

### buildContractsWithAnnexesCte()
Handles the complex contract-annex relationship logic:
```php
private function buildContractsWithAnnexesCte(string $farmYearStart, string $farmYearEnd)
```
- Joins contracts with their annexes
- Handles effective contract ID calculation
- Includes contract group information

### buildPlotOwnerDataCte()
Manages plot, owner, and representative relationships:
```php
private function buildPlotOwnerDataCte(int $year)
```
- Joins plots with owners and representatives
- Includes owner hierarchy calculations
- Handles personal use data

### buildRentCalculationsCte()
Calculates rent values and areas:
```php
private function buildRentCalculationsCte(string $farmYearStart, string $farmYearEnd, int $year)
```
- Handles plot rents and charged rents
- Calculates rent money values
- Includes rent type information

### buildOwnerStatusCte()
Determines owner status and payment eligibility:
```php
private function buildOwnerStatusCte(string $farmYearStart, string $farmYearEnd)
```
- Calculates dead owner status
- Determines payment eligibility
- Handles heritor status

### buildRentaNatAggregationCte()
Creates JSON aggregation for natural rent data:
```php
private function buildRentaNatAggregationCte()
```
- Combines contract rents and plot rents
- Uses UNION ALL for different data sources
- Creates JSON objects for each rent item

### buildChargedRentaNatAggregationCte()
Creates JSON aggregation for charged rent data:
```php
private function buildChargedRentaNatAggregationCte(int $year)
```
- Aggregates charged natural rent data
- Creates JSON objects with conversion status
- Groups by contract, plot, and owner

## Benefits

### Maintainability
- **Modular Design**: Each CTE handles a specific business logic component
- **Readable Code**: Complex queries broken into understandable parts
- **Reusable Components**: CTE methods can be reused across different queries

### Performance
- **Query Optimization**: PostgreSQL can optimize CTEs independently
- **Reduced Complexity**: Simpler execution plans for each CTE
- **Caching**: CTEs can be cached by the query planner

### Testing
- **Unit Testing**: Each CTE method can be tested independently
- **Integration Testing**: Full query testing with known datasets
- **Performance Testing**: Benchmark against original raw SQL

## Usage Examples

### Basic Owner Contracts Query
```php
$repository = new PaymentsRepository();
$contracts = $repository->getOwnerContracts(2023, [
    'owner_id' => 123,
    'payroll_farming' => [1, 2, 3]
]);
```

### Payment Plots with Filters
```php
$plots = $repository->getPaymentPlots(
    year: 2023,
    contractAnnexId: 456,
    ownerId: 123,
    path: null,
    filterParams: [
        'payroll_ekate' => ['12345', '67890'],
        'payroll_farming' => [1, 2]
    ]
);
```

### Payment Plots with JSON Aggregations
```php
$plotsWithJson = $repository->getPaymentPlotsWithJsonAggregations(
    year: 2023,
    contractAnnexId: null,
    ownerId: 123
);
```

## Testing

### Unit Tests
Located in `tests/Unit/PaymentsRepositoryCteTest.php`:
- Tests each CTE method independently
- Validates query structure and results
- Tests parameter validation
- Performance testing

### Integration Tests
Located in `scripts/test_cte_implementation.php`:
- Tests against real database
- Compares results with original implementation
- Performance benchmarking
- Error handling validation

### Running Tests
```bash
# Unit tests
php vendor/bin/phpunit tests/Unit/PaymentsRepositoryCteTest.php

# Integration tests
php scripts/test_cte_implementation.php
```

## Migration Strategy

### Phase 1: Basic CTE Implementation ✅
- Implement getOwnerContracts() with CTE
- Add parameter validation
- Create basic test suite

### Phase 2: Complex Query Conversion ✅
- Convert getPaymentPlots() to use multiple CTEs
- Implement helper methods for each CTE component
- Add comprehensive filtering

### Phase 3: JSON Aggregations 🔄
- Implement JSON aggregation CTEs
- Handle LATERAL joins and UNION ALL
- Preserve original JSON structure

### Phase 4: Testing & Optimization 📋
- Comprehensive test suite
- Performance optimization
- Documentation and training

## Performance Considerations

### Query Optimization
- CTEs are materialized once and reused
- PostgreSQL can optimize each CTE independently
- Window functions work efficiently within CTEs

### Memory Usage
- CTEs use temporary storage for intermediate results
- Large datasets may require memory tuning
- Monitor query execution plans

### Indexing
- Ensure proper indexes on CTE join columns
- Consider partial indexes for filtered CTEs
- Monitor slow query logs

## Troubleshooting

### Common Issues
1. **Missing Fields**: Ensure all required fields are selected in CTEs
2. **Join Conditions**: Verify join conditions between CTEs
3. **Parameter Binding**: Check parameter binding in complex CTEs
4. **PostgreSQL Functions**: Use DB::raw() for PostgreSQL-specific functions

### Debugging
- Use `toSql()` method to inspect generated SQL
- Check query execution plans with EXPLAIN
- Monitor database logs for errors
- Use Laravel's query debugging tools

## Future Enhancements

### Recursive CTEs
- Implement recursive CTEs for hierarchical data
- Owner hierarchy traversal
- Contract dependency chains

### Performance Optimization
- Query plan analysis and optimization
- Index recommendations
- Caching strategies

### Additional Features
- More complex JSON aggregations
- Dynamic CTE composition
- Query result caching
