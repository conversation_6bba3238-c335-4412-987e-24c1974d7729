<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCollections\UserDbCollectionsController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Personal Use Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id personal-use-grid
 */
class PersonalUseGrid extends TRpcApiProvider
{
    private $module = 'Payments';
    private $service_id = 'personal-use-grid';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPersonalUseGrid'],
                'validators' => [
                    'contractId' => 'validateInteger',
                    'year' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'readPersonalUseOwners' => ['method' => [$this, 'readPersonalUseOwners'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'savePersonalUse' => ['method' => [$this, 'savePersonalUse']],
            'deletePersonalUse' => ['method' => [$this, 'deletePersonalUse']],
            'getPersonalUseData' => ['method' => [$this, 'getPersonalUseData']],
            'getPersonalUsePayments' => ['method' => [$this, 'getPersonalUsePayments'],
                'validators' => [
                    'params' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    public function readPersonalUseGrid(int $contractId, int $year, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $options = [
            'tablename' => 'su_contract_owner_rel cor',
            'return' => [
                'so.id as owner_id',
                "(CASE WHEN so.owner_type = 1 THEN so.name || ' ' || so.surname || ' ' || so.lastname ELSE so.company_name END) as owner_names",
                "STRING_AGG(DISTINCT sor.rep_name || ' ' || sor.rep_surname || ' ' || sor.rep_lastname, ', ') as rep_names",
                'ROUND(sum(scpr.area_for_rent * por.percent / 100)::NUMERIC, 3) as owner_area',
                'ROUND(max(pu.area)::NUMERIC, 3) as personal_area',
                'max(pu.pu_ids) as pu_ids',
            ],
            'joins' => [
                'left join su_contracts_plots_rel scpr on scpr.annex_action = \'added\' and scpr.contract_id = cor.contract_id',
                'left join layer_kvs kvs on kvs.gid = scpr.plot_id ',
                'left join su_owners so on so.id = cor.owner_id ',
                'left join su_plots_owners_rel por on por.pc_rel_id = scpr.id and por.owner_id = cor.owner_id',
                'left join su_owners_reps sor on sor.id = por.rep_id',
                'left join lateral (
                    select
                        sum(pur.area) as area,
                        string_agg(distinct pu.id::text, \',\') as pu_ids
                    from su_personal_use pu
                    left join su_personal_use_rents pur on (pur.pu_id = pu.id)
                    inner join su_contracts_plots_rel scpr ON scpr.id = pu.pc_rel_id 
                    where true
                        and scpr.contract_id = cor.contract_id
                        and pu.owner_id = cor.owner_id
                        and pu.year = ' . $year . '
                ) as pu on true',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'cor', 'compare' => '=', 'value' => $contractId],
                'percent' => ['column' => 'percent', 'prefix' => 'por', 'compare' => '>', 'value' => 0],
                'area' => ['column' => 'area', 'prefix' => 'pu', 'compare' => 'is not', 'value' => 'null'],
            ],
            'group' => 'so.id',
        ];

        return $UserDbPaymentsController->getItemsByParams($options);
    }

    /**
     * Read personal use grid.
     *
     * @api-method readPersonalUseOwners
     *
     * @param array $params -Params
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows             -The results
     *               #item string total           -The count of all results
     *               }
     */
    public function readPersonalUseOwners(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $options = [
            'contract_id' => $params['contract_id'],
            'annex_id' => $params['annex_id'],
            'year' => $params['year'],
            'addedOnly' => true,
            'page' => $page,
            'rows' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];

        $contractPaymentsGrid = makeApiClass('payments-rpc', 'contract-payments-grid');
        $ownersTree = $contractPaymentsGrid->getContractPayments($params['contract_id'], $params['annex_id'], $params['year']);
        $ownersWithPersonalUse = $UserDbPaymentsController->getPersonalUseData($options);
        $result = $ownersTree['rows'];
        $this->excludeOwnersWithPersonalUse($result, array_column($ownersWithPersonalUse['rows'], 'owner_id'), $params['year']);
        $this->fixChildrenKeys($result);

        return [
            'rows' => $result,
            'total' => count($result),
            'footer' => [],
        ];
    }

    /**
     * Get personal use payments.
     *
     * @api-method getPersonalUsePayments
     *
     * @param array $params -Params
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows             -The results
     *               #item string total           -The count of all results
     *               }
     */
    public function getPersonalUsePayments(array $params, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // read the inserted personal_uses for that owner and pc_rels so we can use the ids to insert the rentas.
        $options = [
            'tablename' => 'su_transactions tr',
            'return' => [
                'tr.id as transaction_id',
                'p.id as payment_id',
                'o.id as owner_id',
                '(o.name || \' \' || o.surname || \' \' || o.lastname) as owner_names',
                'coalesce(o.egn, o.eik)  as egn_eik',
                'p.farming_year',
                'tr.date',
                'pn.nat_type as nat_type_id',
                'rt.name as nat_type_label',
                'rt.name as nat_type_measure',
                'pn.amount',
                'pn.unit_value',
            ],
            'joins' => [
                'left join su_payments p on p.transaction_id = tr.id',
                'left join su_payments_natura pn on pn.payment_id = p.id',
                'left join su_owners o on o.id = p.owner_id',
                'left join su_renta_types rt on rt.id = pn.nat_type',
            ],
            'where' => [
                'status' => ['column' => 'status', 'prefix' => 'tr', 'compare' => '=', 'value' => 'true'],
                'type' => ['column' => 'type', 'prefix' => 'tr', 'compare' => '=', 'value' => TRANSACTION_TYPE_PERSONAL_USE],
                'farming_year' => ['column' => 'farming_year', 'prefix' => 'tr', 'compare' => '=', 'value' => $params['farming_year']],
                'nat_type' => ['column' => 'nat_type', 'prefix' => 'pn', 'compare' => '=', 'value' => $params['rent_type_id']],
            ],
        ];
        $payments = $UserDbPaymentsController->getItemsByParams($options, false, false);

        foreach ($payments as &$payment) {
            $payment['amount'] = number_format($payment['amount'], 3);
        }

        return [
            'rows' => array_values($payments),
            'total' => count($payments),
        ];
    }

    /**
     * Save personal use.
     *
     * @api-method savePersonalUse
     *
     * @param array $data
     *                    {
     *                    #item int contract_id           -The contract id to save personal use
     *                    #item int owner_id              -The owner id to save personal use
     *                    #item string year               -The year for personal use
     *                    #item array personal_use_plots  -The personal use plots info
     *                    {
     *                    #item string plot_id        -The plot id
     *                    #item string area_type      -The area type
     *                    #item string category       -The category
     *                    #item string contract_area  -The contract area
     *                    #item string ekate          -The ekate
     *                    #item string kad_ident      -The kad ident (ekate.masiv.number)
     *                    #item string personal_use   -The personal use area
     *                    }
     *                    }
     *
     * @return array
     */
    public function savePersonalUse($data)
    {
        // in case info is incorrect
        if (!(int)$data['contract_id'] || !(int)$data['owner_id'] || !(int)$data['year']) {
            return [];
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UsersController = new UsersController('Users');

        $paidPersonalUseResultsAll = $UserDbPaymentsController->getPersonalUsePayments($data);
        if (!empty($paidPersonalUseResultsAll)) {
            throw new MTRpcException('PERSONAL_USE_PAYMENTS_EXIST', -33237);
        }

        foreach ($data['personal_use_plots'] as $plot) {
            if ($plot['personal_use_area'] > 0 && $plot['personal_use_area'] > $plot['contract_area']) {
                throw new MTRpcException('WRONG_PLOT_AREA', -33221);
            }

            if (is_null($plot['personal_use_area']) || '' == trim($plot['personal_use_area'])) {
                throw new MTRpcException('WRONG_PERSONAL_USE_AREA', -33239);
            }
        }

        $data = (object)$data;

        if ($data->pu_ids) {
            $UserDbCollectionsController = new UserDbCollectionsController($this->User->database);
            $UserDbCollectionsController->hasCollectionsForPersonalUse($data->pu_ids);
        }

        $pc_rels = array_map(function ($item) {
            return $item['pc_rel_id'];
        }, $data->personal_use_plots);

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            if ($data->is_personal_use_proportionally_distributed) {
                $unique_pc_rel_ids = [];
                $filtered_data = [];
                foreach ($data->personal_use_plots as $item) {
                    $pc_rel_id = $item['pc_rel_id'];
                    if (!in_array($pc_rel_id, $unique_pc_rel_ids)) {
                        $unique_pc_rel_ids[] = $pc_rel_id;
                        $filtered_data[] = $item;
                    }
                }

                $data->personal_use_plots = $filtered_data;
            }
            $this->reInsertPersonlUse($data->personal_use_plots, $pc_rels, $data->owner_id, $data->year, $data->auto_crops_divide);

            // read the inserted personal_uses for that owner and pc_rels, so we can use the ids to insert the rentas.
            $options = [
                'tablename' => $UserDbController->DbHandler->tablePersonalUse . ' pu',
                'return' => [
                    'pu.id as pu_id, pu.pc_rel_id as pc_rels, rel.contract_area as contract_area',
                ],
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $data->owner_id],
                    'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => 'IN', 'value' => $pc_rels],
                    'year' => ['column' => 'year', 'prefix' => 'pu', 'compare' => '=', 'value' => $data->year],
                ],
                'joins' => [
                    'left join su_contracts_plots_rel rel on rel.id = pu.pc_rel_id',
                ],
            ];
            $inserts = $UserDbController->getItemsByParams($options);

            if ($inserts) {
                $allPersonalRents = [];
                if ($data->is_personal_use_proportionally_distributed) {
                    // $personal_use_rents = $data->rents;
                    $personal_use_rents_arr = array_filter($data->rents, function ($rent) {
                        return !empty($rent);
                    });
                    $hasNoRentaType = false;
                    $personal_use_rents = [];
                    foreach ($personal_use_rents_arr as $key => $value) {
                        if (empty($value['type']) && $hasNoRentaType) {
                            continue;
                        }

                        $personal_use_rents[] = $value;

                        if (empty($value['type'])) {
                            $hasNoRentaType = true;
                        }
                    }

                    $personalUseRentsCount = count($personal_use_rents);
                    if ($personalUseRentsCount) {
                        foreach ($personal_use_rents as $key => $personal_use_rent) {
                            // if (empty($personal_use_rent)) {
                            //     continue;
                            // } // values < 0 are system values. They must not insert in db
                            foreach ($inserts as $personalInsert) {
                                $plotDataToInsert = array_values(array_filter($data->personal_use_plots, function ($item) use ($personalInsert) {
                                    return $item['pc_rel_id'] == $personalInsert['pc_rels'];
                                }));
                                $allPersonalRents[] = [
                                    'pu_id' => $personalInsert['pu_id'],
                                    'renta_type' => !empty($personal_use_rent['type']) ? $personal_use_rent['type'] : null,
                                    'renta_per_dka' => $personal_use_rent['amount'] > 0 ? $personal_use_rent['amount'] : null,
                                    'unit_value' => !empty($personal_use_rent['unit_value']) ? $personal_use_rent['unit_value'] : null,
                                    'average_yield' => !empty($personal_use_rent['average_yield']) ? $personal_use_rent['average_yield'] : null,
                                    'treatments_price' => !empty($personal_use_rent['treatments_price']) ? $personal_use_rent['treatments_price'] : null,
                                    'price_sum' => !empty($personal_use_rent['price_sum']) ? $personal_use_rent['price_sum'] : null,
                                    'area' => $UserDbPaymentsController->calculateProportionsOfPersonalUseArea(
                                        $plotDataToInsert[0]['contract_area'],
                                        $data->used_area,
                                        ($data->auto_crops_divide ? ($data->personal_area / $personalUseRentsCount) : $personal_use_rent['area'])
                                    ),
                                ];
                            }
                        }
                    } else { // remove personal use of owner if all renta types are removed
                        $deleteOptions = [
                            'tablename' => $UserDbController->DbHandler->tablePersonalUse,
                            'id_name' => 'id',
                            'id_string' => implode(',', array_column($inserts, 'pu_id')),
                        ];

                        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $deleteOptions], [], 'Deleting personal use by id');

                        $UserDbController->deleteItemsByParams($deleteOptions);
                    }
                } else {
                    foreach ($inserts as $personalInsert) {
                        $plotDataToInsert = array_values(array_filter($data->personal_use_plots, function ($item) use ($personalInsert) {
                            return $item['pc_rel_id'] == $personalInsert['pc_rels'];
                        }));

                        $allPersonalRents[] = [
                            'pu_id' => $personalInsert['pu_id'],
                            'renta_type' => !empty($plotDataToInsert[0]['renta_type']) ? $plotDataToInsert[0]['renta_type'] : null,
                            'renta_per_dka' => $plotDataToInsert[0]['renta_per_dka'] > 0 ? $plotDataToInsert[0]['renta_per_dka'] : null,
                            'unit_value' => !empty($plotDataToInsert[0]['unit_value']) ? $plotDataToInsert[0]['unit_value'] : null,
                            'average_yield' => !empty($plotDataToInsert[0]['average_yield']) ? $plotDataToInsert[0]['average_yield'] : null,
                            'treatments_price' => !empty($plotDataToInsert[0]['treatments_price']) ? $plotDataToInsert[0]['treatments_price'] : null,
                            'price_sum' => !empty($plotDataToInsert[0]['price_sum']) ? $plotDataToInsert[0]['price_sum'] : null,
                            'area' => $plotDataToInsert[0]['personal_use_area'],
                        ];
                    }
                }

                // write into the rents table
                if (!empty($allPersonalRents)) {
                    $rentsOptions = [
                        'tablename' => 'su_personal_use_rents',
                        'columns' => 'pu_id, renta_type, renta_per_dka, unit_value, average_yield, treatments_price, price_sum, area',
                        'values' => $allPersonalRents,
                    ];
                    $UserDbController->addItems($rentsOptions);
                }
            }

            // delete the contract_owner relation
            $deleteOptions = [
                'tablename' => 'su_contract_owner_rel',
                'id_name' => 'contract_id',
                'id_string' => $data->contract_id,
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $data->owner_id],
                ],
            ];

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $deleteOptions], [], 'Deleting contract owner rel by contract_id and owner_id');

            $UserDbController->deleteItemsByParams($deleteOptions);

            // add the contract_owner_rel
            $options = [
                'tablename' => 'su_contract_owner_rel',
                'mainData' => [
                    'contract_id' => $data->contract_id,
                    'owner_id' => $data->owner_id,
                    'is_personal_use_proportionally_distributed' => $data->is_personal_use_proportionally_distributed,
                ],
            ];

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Adding contract owner rel');

            $UserDbController->addItem($options);
            $transaction->commit();
        } catch (TDbException $e) {
            $transaction->rollback();

            throw $e;
        }
    }

    /**
     * Delete personal use for selected owner.
     *
     * @api-method deletePersonalUse
     *
     * @param int $id
     *
     * @return bool result
     */
    public function deletePersonalUse($data)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbCollectionsController = new UserDbCollectionsController($this->User->database);
        $UsersController = new UsersController('Users');

        $UserDbCollectionsController->hasCollectionsForPersonalUse($data['pu_ids']);

        $paidPersonalUseResultsAll = $UserDbPaymentsController->getPersonalUsePayments($data);
        if (!empty($paidPersonalUseResultsAll)) {
            throw new MTRpcException('PERSONAL_USE_PAYMENTS_EXIST', -33237);
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $data], [], 'Deleting personal use by id');

        $UserDbPaymentsController->deletePersonalUse($data['pu_ids']);

        return true;
    }

    public function getPersonalUseData($data)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $personalUseDistribution = false;
        $personalUsePlots = [];
        $totalPersonalUseArea = 0;
        $autoCropsDivide = true;

        $options = [
            'return' => [
                'kvs.gid',
                'kvs.kad_ident',
                'kvs.ekate',
                'kvs.category',
                'kvs.area_type',
                'scpr.id as pc_rel_id',
                'cor.is_personal_use_proportionally_distributed',
                'sum(scpr.area_for_rent * spor."percent" / 100) as contract_area',
                'COALESCE(pur.area, 0) as personal_use_area',
                'pur.renta_type as renta_type',
                'pur.average_yield as average_yield',
                'pur.treatments_price as treatments_price',
                'pur.price_sum as price_sum',
                'pur.renta_per_dka as renta_per_dka',
                'pur.unit_value as unit_value',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'scpr', 'compare' => '=', 'value' => $data['contract_id']],
                'owner_id' => ['column' => 'owner_id', 'prefix' => 'spor', 'compare' => '=', 'value' => $data['owner_id']],
                'dead_parent' => ['column' => 'not EXISTS ( SELECT 1  FROM su_owners so WHERE so.id != spor.owner_id and so.id = any (string_to_array(spor.path::text, \'.\' )::int[]) AND so.dead_date >= \'' . $GLOBALS['Farming']['years'][$data['year']]['start_date'] . '\')', 'compare' => '=', 'value' => true],
            ],
            'year' => $data['year'],
            'group' => 'pur.id, scpr.id, kvs.gid, cor.is_personal_use_proportionally_distributed',
        ];

        $resultPersonalUseDistribution = $UserDbPaymentsController->getContractOwnerDistributionData($options, false, false);

        if ($resultPersonalUseDistribution[0]['is_personal_use_proportionally_distributed']) {
            $personalUsePlots = $UserDbPaymentsController->getPersonalUseDataByCrop($data);
            $personalUseDistribution = $resultPersonalUseDistribution[0]['is_personal_use_proportionally_distributed'];
        }

        foreach ($personalUsePlots as $personalUsePlot) {
            $totalPersonalUseArea += $personalUsePlot['area'];
            $autoCropsDivide = $personalUsePlot['auto_crops_divide'];
        }

        return [
            'is_proportionally_distributed' => $personalUseDistribution,
            'auto_crops_divide' => $autoCropsDivide,
            'rents' => $personalUsePlots,
            'rows' => $resultPersonalUseDistribution,
            'total_personal_use_area' => $totalPersonalUseArea,
            'total_owned_area' => round(array_sum(array_column($resultPersonalUseDistribution, 'contract_area')) / count(array_unique(array_column($resultPersonalUseDistribution, 'renta_type'))), 3),
        ];
    }

    private function excludeOwnersWithPersonalUse(array &$owners, array $OwnerIdsWithPersonalUse, $year)
    {
        foreach ($owners as $key => &$owner) {
            if ($owner['is_dead']) {
                $deadDate = $owner['dead_date'];
                if (!$deadDate) {
                    $deadDate = '2023-01-01';
                }

                $deadFarmingYear = getFarmngYearFromDate($deadDate);
                if ($deadFarmingYear['id'] == $year) {
                    unset($owners[$key]);

                    continue;
                }
            }
            if (in_array($owner['owner_id'], $OwnerIdsWithPersonalUse)) {
                unset($owners[$key]);

                continue;
            }

            if (isset($owner['children']) && is_array($owner['children'])) {
                $this->excludeOwnersWithPersonalUse($owner['children'], $OwnerIdsWithPersonalUse, $year);
                if (empty($owner['children'])) {
                    unset($owner['children']);
                }
            }
        }
    }

    private function fixChildrenKeys(array &$owners)
    {
        $owners = array_values($owners);
        foreach ($owners as &$owner) {
            if (isset($owner['children']) && is_array($owner['children'])) {
                $this->fixChildrenKeys($owner['children']);
            }
        }
    }

    private function reInsertPersonlUse($personal_use_plots, $pc_rels, $owner_id, $year, $auto_crops_divide)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $deleteOptions = [
            'tablename' => $UserDbController->DbHandler->tablePersonalUse,
            'id_name' => 'pc_rel_id',
            'id_string' => implode(',', $pc_rels),
            'where' => [
                'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $owner_id],
                'year' => ['column' => 'year', 'compare' => '=', 'value' => $year],
            ],
        ];

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['options' => $deleteOptions], [], 'Deleting personal use for owner');

        $UserDbController->deleteItemsByParams($deleteOptions);

        // add them anew; add the rents for those personal use
        $options = [];
        $options['tablename'] = $UserDbController->DbHandler->tablePersonalUse;
        $options['columns'] = 'owner_id, year, pc_rel_id, auto_crops_divide';

        $personalUseValues = array_map(function ($item) use ($owner_id, $year, $auto_crops_divide) {
            return [
                'owner_id' => $owner_id,
                'year' => $year,
                'pc_rel_id' => $item['pc_rel_id'],
                'auto_crops_divide' => ($auto_crops_divide ? true : false),
            ];
        }, $personal_use_plots);

        $options['values'] = $personalUseValues;

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Deleting personal use for owner');

        $UserDbController->addItems($options);
    }
}
