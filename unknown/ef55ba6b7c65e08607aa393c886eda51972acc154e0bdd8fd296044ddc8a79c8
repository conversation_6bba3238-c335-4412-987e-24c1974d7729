<?php

namespace TF\Commands\Common;

use PDO;

class CreateWorkableAndNotWorkableAreaSpecificRentsCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:create-workable-and-not-workable-area-specific-rents')
            ->setDescription('Create workable and not workable area specific rents');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $wrongAreasCmd = $pdo->prepare(
            'select 
                        rel.id as pc_rel_id,
                        rel.plot_id,
                        kvs.kad_ident,
                        rel.contract_id,
                        rel.contract_area,
                        kvs.document_area 
                    from su_contracts_plots_rel rel
                    left join layer_kvs kvs on rel.plot_id = kvs.gid 
                    where rel.contract_area > document_area;'
        );
        $wrongAreasCmd->execute();
        $wrongAreasArr = $wrongAreasCmd->fetchAll(PDO::FETCH_ASSOC);

        if (count($wrongAreasArr)) {
            $output->writeln('Wrong areas: ' . count($wrongAreasArr));
            // Log the wrong areas in file
            $logName = $this->userDbName . '_wrong_areas';
            $output->writeln('Log the wrong areas in file: ' . $logName);
            parent::logging(date('Y-m-d H:i:s') . PHP_EOL . json_encode($wrongAreasArr), $logName);
        }

        $output->writeln('Check are there specific rents to create...');
        $rentsCmd = $pdo->prepare("select 
            scrt.id as rent_type_id,
            scrt.\"type\",
            scrt.value,
            scpr.id as pc_rel_id,
            scpr.contract_area,
            scpr.area_for_rent,
            scpr.kvs_allowable_area,
            (case 
                when (scrt.\"type\" = 'arable' and scrt.value = 'true') then scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area) 
                else (case when (scpr.area_for_rent - (scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area)) < 0) then 0 else scpr.area_for_rent - (scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area)) end)
            end) as area
        from su_contracts_plots_rel scpr
        inner join su_contracts_rents_types scrt on scrt.contract_id = scpr.contract_id
        left join layer_kvs lk on lk.gid = scpr.plot_id; ");

        $rentsCmd->execute();
        $rents = $rentsCmd->fetchAll(PDO::FETCH_ASSOC);

        if (!count($rents)) {
            $output->writeln('No specific rents to create!');

            return;
        }

        $output->writeln('Fix wrong areas...');
        $pdo->exec(
            'update layer_kvs 
                set document_area = rel.contract_area
                from su_contracts_plots_rel rel
                where 
                    rel.plot_id = layer_kvs.gid 
                    and rel.contract_area > document_area'
        );
        $output->writeln('Done');

        $output->writeln('Create rents...');
        foreach ($rents as $rent) {
            $pdo->exec("INSERT INTO public.su_plots_rents (pc_rel_id,rent_type_id,area) VALUES ({$rent['pc_rel_id']},{$rent['rent_type_id']},{$rent['area']}); ");
            // $pdo->exec("UPDATE public.su_plots_rents SET area = {$rent['area']} WHERE pc_rel_id = {$rent['pc_rel_id']} AND rent_type_id = {$rent['rent_type_id']};");
        }
        $output->writeln('Done');
    }
}
