<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfNaNPercentsInOwnersTree extends UserDbCommand
{
    private static $tmpTableName = 'tmp_statistic_of_nan_percents_in_owners_tree_';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_nan_percents_in_owners_tree')
            ->setDescription('Generate statistic of NaN percents in owners tree and insert the info in ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $data = $this->getData($pdo);

        $reportSql = $this->mainConnection->prepare('
            INSERT INTO ' . self::$tmpTableName . ' (
                database,
                owner_names,
                contract_id,
                contract_num,
                contract_start_date,
                contract_due_date,
                contract_created_at,
                contract_updated_at,
                plot_id,
                kad_ident
            )
            VALUES (
                :database,
                :owner_names,
                :contract_id,
                :contract_num,
                :contract_start_date,
                :contract_due_date,
                :contract_created_at,
                :contract_updated_at,
                :plot_id,
                :kad_ident
            );
        ');

        $output->writeln('In database ' . $this->userDbName . ' has ' . count($data) . ' owners' . PHP_EOL);

        foreach ($data as $record) {
            $reportSql->execute([
                'database' => $this->userDbName,
                'owner_names' => $record['owner_names'],
                'contract_id' => $record['contract_id'],
                'contract_num' => $record['contract_num'],
                'contract_start_date' => $record['contract_start_date'],
                'contract_due_date' => $record['contract_due_date'],
                'contract_created_at' => $record['contract_created_at'],
                'contract_updated_at' => $record['contract_updated_at'],
                'plot_id' => $record['plot_id'],
                'kad_ident' => $record['kad_ident'],
            ]);
        }
    }

    protected function onCommandStart($input, $output)
    {
        self::$tmpTableName .= time();

        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NULL,
                owner_names varchar NULL,
                contract_id int4 NULL,
                contract_num varchar NULL,
                contract_start_date varchar NULL,
                contract_due_date varchar NULL,
                contract_created_at varchar NULL,
                contract_updated_at varchar NULL,
                plot_id int4 NULL,
                kad_ident varchar NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    protected function getData($pdo)
    {
        $cmd = $pdo->prepare('
            select
                so."name" || \' \' || so.surname || \' \' || so.lastname as owner_names,
                scpr.contract_id,
                sc.c_num as contract_num,
                to_char(sc.start_date, \'YYYY-MM-DD\') as contract_start_date,
                to_char(sc.due_date, \'YYYY-MM-DD\') as contract_due_date,
                to_char(sc.created_at , \'YYYY-MM-DD\') as contract_created_at,
                to_char(sc.updated_at , \'YYYY-MM-DD\') as contract_updated_at,
                scpr.plot_id,
                lk.kad_ident
            from su_plots_owners_rel spor
            left join su_contracts_plots_rel scpr on scpr.id = spor.pc_rel_id
            left join su_contracts sc on sc.id = scpr.contract_id
            left join layer_kvs lk on lk.gid = scpr.plot_id
            left join su_owners so on so.id = spor.owner_id
            where
                spor.path is null and
                (
                    SELECT count(h.id)
                    FROM su_heritors h
                    WHERE h.path ~ (spor.owner_id::text || \'.*\')::lquery
                ) > 1
                and (
                    SELECT
                        count(spor_h)
                    FROM su_plots_owners_rel spor_h
                    WHERE
                        spor_h.pc_rel_id = spor.pc_rel_id
                ) = 1;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
