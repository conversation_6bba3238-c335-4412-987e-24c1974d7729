<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfRentedPlotsAndOwnersCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_statisctic_of_rented_plots_and_owners';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_rented_plots_and_owners')
            ->setDescription('Generate statistics of contracts with rented plots and owners and publish the results in table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $plots = $this->getContractsPlots($pdo);

        foreach ($plots as $plot) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        owner_id,
                        owner_names,
                        percent,
                        kad_ident,
                        document_area,
                        contract_area,
                        contract_id,
                        c_num,
                        contract_type,
                        start_date,
                        due_date
                    )
                    VALUES (
                        :database,
                        :owner_id,
                        :owner_names,
                        :percent,
                        :kad_ident,
                        :document_area,
                        :contract_area,
                        :contract_id,
                        :c_num,
                        :contract_type,
                        :start_date,
                        :due_date
                    );
                ');

            $sql->execute([
                'database' => $this->userDbName,
                'owner_id' => $plot['owner_id'],
                'owner_names' => $plot['owner_names'],
                'percent' => $plot['percent'],
                'kad_ident' => $plot['kad_ident'],
                'document_area' => $plot['document_area'],
                'contract_area' => $plot['contract_area'],
                'contract_id' => $plot['contract_id'],
                'c_num' => $plot['c_num'],
                'contract_type' => $plot['contract_type'],
                'start_date' => $plot['start_date'],
                'due_date' => $plot['due_date'],
            ]);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NOT NULL,
                owner_id int DEFAULT NULL,
                owner_names varchar DEFAULT NULL,
                percent varchar DEFAULT NULL,
                kad_ident varchar DEFAULT NULL,
                document_area numeric DEFAULT NULL,
                contract_area numeric DEFAULT NULL,
                contract_id int DEFAULT NULL,
                c_num varchar DEFAULT NULL,
                contract_type int DEFAULT NULL,
                start_date date DEFAULT NULL,
                due_date date DEFAULT NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractsPlots($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                so.id as owner_id,
                concat(so."name", \' \', so.surname, \' \' , so.lastname) as owner_names,
                spor."percent",
                lk.kad_ident,
                lk.document_area,
                scpr.contract_area,
                sc.id as contract_id,
                sc.c_num as c_num,
                sc.nm_usage_rights as contract_type,
                to_char(sc.start_date, \'YYYY-MM-DD\') as start_date,
                to_char(sc.due_date, \'YYYY-MM-DD\') as due_date
            from su_contracts_plots_rel scpr 
            left join layer_kvs lk on lk.gid = scpr.plot_id 
            inner join su_plots_owners_rel spor on spor.pc_rel_id = scpr.id and spor.is_heritor = false
            left join su_owners so on so.id = spor.owner_id 
            left join su_contracts sc on sc.id = scpr.contract_id
            where lk.kad_ident is not null; 
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
