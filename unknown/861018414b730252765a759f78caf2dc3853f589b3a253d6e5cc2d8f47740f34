<?php

namespace TF\Commands\v5_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS5236Command extends UserDbCommand
{
    public const AGRODIMEX_DB = 'db_bg_8843';

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5.1:GPS-5236')
            ->setDescription('Add specific rents in account agrodimex');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        if (self::AGRODIMEX_DB != $this->userDbName) {
            $output->writeln('The script works only for db: ' . $this->userDbName);
            exit;
        }

        $rentsCmd = $pdo->prepare("select 
            scrt.id as rent_type_id,
            scrt.\"type\",
            scrt.value,
            scpr.id as pc_rel_id,
            scpr.contract_area,
            scpr.area_for_rent,
            scpr.kvs_allowable_area,
            (case 
                when (scrt.\"type\" = 'arable' and scrt.value = 'true') then scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area) 
                else (case when (scpr.area_for_rent - (scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area)) < 0) then 0 else scpr.area_for_rent - (scpr.contract_area * (scpr.kvs_allowable_area / lk.document_area)) end)
            end) as area
        from su_contracts_plots_rel scpr
        inner join su_contracts_rents_types scrt on scrt.contract_id = scpr.contract_id
        left join layer_kvs lk on lk.gid = scpr.plot_id; ");

        $rentsCmd->execute();
        $rents = $rentsCmd->fetchAll(PDO::FETCH_ASSOC);

        foreach ($rents as $rent) {
            $pdo->exec("INSERT INTO public.su_plots_rents (pc_rel_id,rent_type_id,area) VALUES ({$rent['pc_rel_id']},{$rent['rent_type_id']},{$rent['area']}); ");
            // $pdo->exec("UPDATE public.su_plots_rents SET area = {$rent['area']} WHERE pc_rel_id = {$rent['pc_rel_id']} AND rent_type_id = {$rent['rent_type_id']};");
        }
    }
}
